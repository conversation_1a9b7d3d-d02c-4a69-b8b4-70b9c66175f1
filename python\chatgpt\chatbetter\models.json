[{"id": "chatbetter.chat_better", "name": "Auto Select Model", "object": "model", "created": 1742327511, "owned_by": "openai", "pipe": {"type": "pipe"}, "info": {"meta": {}}, "actions": [], "tags": []}, {"id": "claude-3.7-sonnet:thinking", "object": "model", "created": **********, "owned_by": "anthropic", "name": "Anthropic: <PERSON> 3.7 <PERSON><PERSON> (thinking)", "openai": {"id": "claude-3.7-sonnet:thinking", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Claude 3.7 Sonnet is an advanced large language model with improved reasoning, coding, and problem-solving capabilities. It introduces a hybrid reasoning approach, allowing users to choose between rapid responses and extended, step-by-step processing for complex tasks. The model demonstrates notable improvements in coding, particularly in front-end development and full-stack updates, and excels in agentic workflows, where it can autonomously navigate multi-step processes.\n\nClaude 3.7 Sonnet maintains performance parity with its predecessor in standard mode while offering an extended reasoning mode for enhanced accuracy in math, coding, and instruction-following tasks.", "modalities": {"input": ["text", "image"], "output": ["text"]}, "provider": "anthropic", "profile_image_url": "/static/anthropic.png"}}, "context_length": 200000, "recommended_model": true, "reasoning_level": "heavy", "auto_select_priority": 0, "premium_model": false, "actions": [], "tags": []}, {"id": "claude-sonnet-4", "object": "model", "created": **********, "owned_by": "anthropic", "name": "Anthropic: <PERSON> 4", "openai": {"id": "claude-sonnet-4", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Claude Sonnet 4 significantly enhances the capabilities of its predecessor, Sonnet 3.7, excelling in both coding and reasoning tasks with improved precision and controllability. Achieving state-of-the-art performance on SWE-bench (72.7%), Sonnet 4 balances capability and computational efficiency, making it suitable for a broad range of applications from routine coding tasks to complex software development projects. Key enhancements include improved autonomous codebase navigation, reduced error rates in agent-driven workflows, and increased reliability in following intricate instructions. Sonnet 4 is optimized for practical everyday use, providing advanced reasoning capabilities while maintaining efficiency and responsiveness in diverse internal and external scenarios.\n\nRead more at the [blog post here](https://www.anthropic.com/news/claude-4)", "modalities": {"input": ["image", "text"], "output": ["text"]}, "provider": "anthropic", "profile_image_url": "/static/anthropic.png"}}, "context_length": 200000, "recommended_model": true, "reasoning_level": "fast", "auto_select_priority": 0, "premium_model": false, "actions": [], "tags": []}, {"id": "llama-4-maverick", "object": "model", "created": **********, "owned_by": "meta-llama", "name": "Meta: Llama 4 Maverick", "openai": {"id": "llama-4-maverick", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Llama 4 Maverick 17B Instruct (128E) is a high-capacity multimodal language model from Meta, built on a mixture-of-experts (MoE) architecture with 128 experts and 17 billion active parameters per forward pass (400B total). It supports multilingual text and image input, and produces multilingual text and code output across 12 supported languages. Optimized for vision-language tasks, Maverick is instruction-tuned for assistant-like behavior, image reasoning, and general-purpose multimodal interaction.\n\nMaverick features early fusion for native multimodality and a 1 million token context window. It was trained on a curated mixture of public, licensed, and Meta-platform data, covering ~22 trillion tokens, with a knowledge cutoff in August 2024. Released on April 5, 2025 under the Llama 4 Community License, Maverick is suited for research and commercial applications requiring advanced multimodal understanding and high model throughput.", "modalities": {"input": ["text", "image"], "output": ["text"]}, "provider": "meta-llama", "profile_image_url": "/static/meta.png"}}, "context_length": 131072, "recommended_model": true, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "gpt-4o", "object": "model", "created": **********, "owned_by": "openai", "name": "OpenAI: GPT-4o", "openai": {"id": "gpt-4o", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "GPT-4o (\"o\" for \"omni\") is OpenAI's latest AI model, supporting both text and image inputs with text outputs. It maintains the intelligence level of [GPT-4 Turbo](/models/openai/gpt-4-turbo) while being twice as fast and 50% more cost-effective. GPT-4o also offers improved performance in processing non-English languages and enhanced visual capabilities.\n\nFor benchmarking against other models, it was briefly called [\"im-also-a-good-gpt2-chatbot\"](https://twitter.com/LiamFedus/status/1790064963966370209)\n\n#multimodal", "modalities": {"input": ["text", "image", "file"], "output": ["text"]}, "provider": "openai", "profile_image_url": "/static/openai.png"}}, "context_length": 128000, "recommended_model": true, "reasoning_level": "fast", "auto_select_priority": 0, "premium_model": false, "actions": [], "tags": []}, {"id": "sonar-deep-research", "object": "model", "created": **********, "owned_by": "perplexity", "name": "Perplexity: Sonar Deep Research", "openai": {"id": "sonar-deep-research", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Sonar Deep Research is a research-focused model designed for multi-step retrieval, synthesis, and reasoning across complex topics. It autonomously searches, reads, and evaluates sources, refining its approach as it gathers information. This enables comprehensive report generation across domains like finance, technology, health, and current events.\n\nNotes on Pricing ([Source](https://docs.perplexity.ai/guides/pricing#detailed-pricing-breakdown-for-sonar-deep-research)) \n- Input tokens comprise of Prompt tokens (user prompt) + Citation tokens (these are processed tokens from running searches)\n- Deep Research runs multiple searches to conduct exhaustive research. Searches are priced at $5/1000 searches. A request that does 30 searches will cost $0.15 in this step.\n- Reasoning is a distinct step in Deep Research since it does extensive automated reasoning through all the material it gathers during its research phase. Reasoning tokens here are a bit different than the CoTs in the answer - these are tokens that we use to reason through the research material prior to generating the outputs via the CoTs. Reasoning tokens are priced at $3/1M tokens", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "perplexity", "profile_image_url": "/static/perplexity.png"}}, "context_length": 200000, "recommended_model": true, "reasoning_level": "heavy", "auto_select_priority": 1, "premium_model": false, "actions": [], "tags": []}, {"id": "sonar-pro", "object": "model", "created": **********, "owned_by": "perplexity", "name": "Perplexity: Sonar Pro", "openai": {"id": "sonar-pro", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Note: Sonar Pro pricing includes Perplexity search pricing. See [details here](https://docs.perplexity.ai/guides/pricing#detailed-pricing-breakdown-for-sonar-reasoning-pro-and-sonar-pro)\n\nFor enterprises seeking more advanced capabilities, the Sonar Pro API can handle in-depth, multi-step queries with added extensibility, like double the number of citations per search as Sonar on average. Plus, with a larger context window, it can handle longer and more nuanced searches and follow-up questions. ", "modalities": {"input": ["text", "image"], "output": ["text"]}, "provider": "perplexity", "profile_image_url": "/static/perplexity.png"}}, "context_length": 200000, "recommended_model": true, "reasoning_level": "light", "auto_select_priority": 0, "premium_model": false, "actions": [], "tags": []}, {"id": "aion-1.0", "object": "model", "created": **********, "owned_by": "aion-labs", "name": "AionLabs: Aion-1.0", "openai": {"id": "aion-1.0", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Aion-1.0 is a multi-model system designed for high performance across various tasks, including reasoning and coding. It is built on DeepSeek-R1, augmented with additional models and techniques such as Tree of Thoughts (ToT) and Mixture of Experts (MoE). It is Aion Lab's most powerful reasoning model.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "aion-labs", "profile_image_url": "/static/favicon.png"}}, "context_length": 131072, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "aion-1.0-mini", "object": "model", "created": **********, "owned_by": "aion-labs", "name": "AionLabs: Aion-1.0-Mini", "openai": {"id": "aion-1.0-mini", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Aion-1.0-Mini 32B parameter model is a distilled version of the DeepSeek-R1 model, designed for strong performance in reasoning domains such as mathematics, coding, and logic. It is a modified variant of a FuseAI model that outperforms R1-Distill-Qwen-32B and R1-Distill-Llama-70B, with benchmark results available on its [Hugging Face page](https://huggingface.co/FuseAI/FuseO1-DeepSeekR1-QwQ-SkyT1-32B-Preview), independently replicated for verification.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "aion-labs", "profile_image_url": "/static/favicon.png"}}, "context_length": 131072, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "nova-lite-v1", "object": "model", "created": **********, "owned_by": "amazon", "name": "Amazon: Nova Lite 1.0", "openai": {"id": "nova-lite-v1", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Amazon Nova Lite 1.0 is a very low-cost multimodal model from Amazon that focused on fast processing of image, video, and text inputs to generate text output. Amazon Nova Lite can handle real-time customer interactions, document analysis, and visual question-answering tasks with high accuracy.\n\nWith an input context of 300K tokens, it can analyze multiple images or up to 30 minutes of video in a single input.", "modalities": {"input": ["text", "image"], "output": ["text"]}, "provider": "amazon", "profile_image_url": "/static/amazon.png"}}, "context_length": 300000, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "nova-micro-v1", "object": "model", "created": **********, "owned_by": "amazon", "name": "Amazon: Nova Micro 1.0", "openai": {"id": "nova-micro-v1", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Amazon Nova Micro 1.0 is a text-only model that delivers the lowest latency responses in the Amazon Nova family of models at a very low cost. With a context length of 128K tokens and optimized for speed and cost, Amazon Nova Micro excels at tasks such as text summarization, translation, content classification, interactive chat, and brainstorming. It has  simple mathematical reasoning and coding abilities.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "amazon", "profile_image_url": "/static/amazon.png"}}, "context_length": 128000, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "nova-pro-v1", "object": "model", "created": **********, "owned_by": "amazon", "name": "Amazon: Nova Pro 1.0", "openai": {"id": "nova-pro-v1", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Amazon Nova Pro 1.0 is a capable multimodal model from Amazon focused on providing a combination of accuracy, speed, and cost for a wide range of tasks. As of December 2024, it achieves state-of-the-art performance on key benchmarks including visual question answering (TextVQA) and video understanding (VATEX).\n\nAmazon Nova Pro demonstrates strong capabilities in processing both visual and textual information and at analyzing financial documents.\n\n**NOTE**: Video input is not supported at this time.", "modalities": {"input": ["text", "image"], "output": ["text"]}, "provider": "amazon", "profile_image_url": "/static/amazon.png"}}, "context_length": 300000, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "claude-3.5-haiku", "object": "model", "created": **********, "owned_by": "anthropic", "name": "Anthropic: <PERSON> 3.5 <PERSON><PERSON>", "openai": {"id": "claude-3.5-haiku", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Claude 3.5 Haiku features offers enhanced capabilities in speed, coding accuracy, and tool use. Engineered to excel in real-time applications, it delivers quick response times that are essential for dynamic tasks such as chat interactions and immediate coding suggestions.\n\nThis makes it highly suitable for environments that demand both speed and precision, such as software development, customer service bots, and data management systems.\n\nThis model is currently pointing to [Claude 3.5 Haiku (2024-10-22)](/anthropic/claude-3-5-haiku-20241022).", "modalities": {"input": ["text", "image"], "output": ["text"]}, "provider": "anthropic", "profile_image_url": "/static/anthropic.png"}}, "context_length": 200000, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "claude-3.5-sonnet", "object": "model", "created": **********, "owned_by": "anthropic", "name": "Anthropic: <PERSON> 3.5 <PERSON><PERSON>", "openai": {"id": "claude-3.5-sonnet", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "New Claude 3.5 Sonnet delivers better-than-Opus capabilities, faster-than-Sonnet speeds, at the same Sonnet prices. Sonnet is particularly good at:\n\n- Coding: Scores ~49% on SWE-Bench Verified, higher than the last best score, and without any fancy prompt scaffolding\n- Data science: Augments human data science expertise; navigates unstructured data while using multiple tools for insights\n- Visual processing: excelling at interpreting charts, graphs, and images, accurately transcribing text to derive insights beyond just the text alone\n- Agentic tasks: exceptional tool use, making it great at agentic tasks (i.e. complex, multi-step problem solving tasks that require engaging with other systems)\n\n#multimodal", "modalities": {"input": ["text", "image"], "output": ["text"]}, "provider": "anthropic", "profile_image_url": "/static/anthropic.png"}}, "context_length": 200000, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "claude-3.7-sonnet", "object": "model", "created": **********, "owned_by": "anthropic", "name": "Anthropic: <PERSON> 3.7 <PERSON><PERSON>", "openai": {"id": "claude-3.7-sonnet", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Claude 3.7 Sonnet is an advanced large language model with improved reasoning, coding, and problem-solving capabilities. It introduces a hybrid reasoning approach, allowing users to choose between rapid responses and extended, step-by-step processing for complex tasks. The model demonstrates notable improvements in coding, particularly in front-end development and full-stack updates, and excels in agentic workflows, where it can autonomously navigate multi-step processes. \n\nClaude 3.7 Sonnet maintains performance parity with its predecessor in standard mode while offering an extended reasoning mode for enhanced accuracy in math, coding, and instruction-following tasks.\n\nRead more at the [blog post here](https://www.anthropic.com/news/claude-3-7-sonnet)", "modalities": {"input": ["text", "image"], "output": ["text"]}, "provider": "anthropic", "profile_image_url": "/static/anthropic.png"}}, "context_length": 200000, "reasoning_level": "heavy", "premium_model": false, "actions": [], "tags": []}, {"id": "claude-opus-4", "object": "model", "created": **********, "owned_by": "anthropic", "name": "Anthropic: <PERSON> 4", "openai": {"id": "claude-opus-4", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Claude Opus 4 is benchmarked as the world’s best coding model, at time of release, bringing sustained performance on complex, long-running tasks and agent workflows. It sets new benchmarks in software engineering, achieving leading results on SWE-bench (72.5%) and Terminal-bench (43.2%). Opus 4 supports extended, agentic workflows, handling thousands of task steps continuously for hours without degradation. \n\nRead more at the [blog post here](https://www.anthropic.com/news/claude-4)", "modalities": {"input": ["image", "text"], "output": ["text"]}, "provider": "anthropic", "profile_image_url": "/static/anthropic.png"}}, "context_length": 200000, "reasoning_level": "heavy", "premium_model": true, "actions": [], "tags": []}, {"id": "arcee-blitz", "object": "model", "created": **********, "owned_by": "arcee-ai", "name": "Arcee AI: <PERSON><PERSON>", "openai": {"id": "arcee-blitz", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Arcee Blitz is a 24 B‑parameter dense model distilled from DeepSeek and built on Mistral architecture for \"everyday\" chat. The distillation‑plus‑refinement pipeline trims compute while keeping DeepSeek‑style reasoning, so Blitz punches above its weight on MMLU, GSM‑8K and BBH compared with other mid‑size open models. With a default 128 k context window and competitive throughput, it serves as a cost‑efficient workhorse for summarization, brainstorming and light code help. Internally, Arcee uses Blitz as the default writer in Conductor pipelines when the heavier Virtuoso line is not required. Users therefore get near‑70 B quality at ~⅓ the latency and price. ", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "arcee-ai", "profile_image_url": "/static/favicon.png"}}, "context_length": 32768, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "caller-large", "object": "model", "created": **********, "owned_by": "arcee-ai", "name": "Arcee AI: <PERSON><PERSON>", "openai": {"id": "caller-large", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Caller Large is Arcee's specialist \"function‑calling\" SLM built to orchestrate external tools and APIs. Instead of maximizing next‑token accuracy, training focuses on structured JSON outputs, parameter extraction and multi‑step tool chains, making Caller a natural choice for retrieval‑augmented generation, robotic process automation or data‑pull chatbots. It incorporates a routing head that decides when (and how) to invoke a tool versus answering directly, reducing hallucinated calls. The model is already the backbone of Arcee Conductor's auto‑tool mode, where it parses user intent, emits clean function signatures and hands control back once the tool response is ready. Developers thus gain an OpenAI‑style function‑calling UX without handing requests to a frontier‑scale model. ", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "arcee-ai", "profile_image_url": "/static/favicon.png"}}, "context_length": 32768, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "coder-large", "object": "model", "created": **********, "owned_by": "arcee-ai", "name": "Arcee AI: <PERSON><PERSON>", "openai": {"id": "coder-large", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Coder‑Large is a 32 B‑parameter offspring of Qwen 2.5‑Instruct that has been further trained on permissively‑licensed GitHub, CodeSearchNet and synthetic bug‑fix corpora. It supports a 32k context window, enabling multi‑file refactoring or long diff review in a single call, and understands 30‑plus programming languages with special attention to TypeScript, Go and Terraform. Internal benchmarks show 5–8 pt gains over CodeLlama‑34 B‑Python on HumanEval and competitive BugFix scores thanks to a reinforcement pass that rewards compilable output. The model emits structured explanations alongside code blocks by default, making it suitable for educational tooling as well as production copilot scenarios. Cost‑wise, Together AI prices it well below proprietary incumbents, so teams can scale interactive coding without runaway spend. ", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "arcee-ai", "profile_image_url": "/static/favicon.png"}}, "context_length": 32768, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "maestro-reasoning", "object": "model", "created": **********, "owned_by": "arcee-ai", "name": "Arcee AI: <PERSON><PERSON> Reasoning", "openai": {"id": "maestro-reasoning", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Maestro Reasoning is Arcee's flagship analysis model: a 32 B‑parameter derivative of Qwen 2.5‑32 B tuned with DPO and chain‑of‑thought RL for step‑by‑step logic. Compared to the earlier 7 B preview, the production 32 B release widens the context window to 128 k tokens and doubles pass‑rate on MATH and GSM‑8K, while also lifting code completion accuracy. Its instruction style encourages structured \"thought → answer\" traces that can be parsed or hidden according to user preference. That transparency pairs well with audit‑focused industries like finance or healthcare where seeing the reasoning path matters. In Arcee Conductor, <PERSON><PERSON> is automatically selected for complex, multi‑constraint queries that smaller SLMs bounce. ", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "arcee-ai", "profile_image_url": "/static/favicon.png"}}, "context_length": 131072, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "spotlight", "object": "model", "created": **********, "owned_by": "arcee-ai", "name": "Arcee AI: Spotlight", "openai": {"id": "spotlight", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Spotlight is a 7‑billion‑parameter vision‑language model derived from Qwen 2.5‑VL and fine‑tuned by Arcee AI for tight image‑text grounding tasks. It offers a 32 k‑token context window, enabling rich multimodal conversations that combine lengthy documents with one or more images. Training emphasized fast inference on consumer GPUs while retaining strong captioning, visual‐question‑answering, and diagram‑analysis accuracy. As a result, Spotlight slots neatly into agent workflows where screenshots, charts or UI mock‑ups need to be interpreted on the fly. Early benchmarks show it matching or out‑scoring larger VLMs such as LLaVA‑1.6 13 B on popular VQA and POPE alignment tests. ", "modalities": {"input": ["image", "text"], "output": ["text"]}, "provider": "arcee-ai", "profile_image_url": "/static/favicon.png"}}, "context_length": 131072, "reasoning_level": "light", "premium_model": false, "actions": [], "tags": []}, {"id": "virtuoso-large", "object": "model", "created": **********, "owned_by": "arcee-ai", "name": "Arcee AI: Virtuoso <PERSON>", "openai": {"id": "virtuoso-large", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Virtuoso‑Large is Arcee's top‑tier general‑purpose LLM at 72 B parameters, tuned to tackle cross‑domain reasoning, creative writing and enterprise QA. Unlike many 70 B peers, it retains the 128 k context inherited from Qwen 2.5, letting it ingest books, codebases or financial filings wholesale. Training blended DeepSeek R1 distillation, multi‑epoch supervised fine‑tuning and a final DPO/RLHF alignment stage, yielding strong performance on BIG‑Bench‑Hard, GSM‑8K and long‑context Needle‑In‑Haystack tests. Enterprises use Virtuoso‑Large as the \"fallback\" brain in Conductor pipelines when other SLMs flag low confidence. Despite its size, aggressive KV‑cache optimizations keep first‑token latency in the low‑second range on 8× H100 nodes, making it a practical production‑grade powerhouse.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "arcee-ai", "profile_image_url": "/static/favicon.png"}}, "context_length": 131072, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "virtuoso-medium-v2", "object": "model", "created": **********, "owned_by": "arcee-ai", "name": "Arcee AI: Virtuoso Medium V2", "openai": {"id": "virtuoso-medium-v2", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Virtuoso‑Medium‑v2 is a 32 B model distilled from DeepSeek‑v3 logits and merged back onto a Qwen 2.5 backbone, yielding a sharper, more factual successor to the original Virtuoso Medium. The team harvested ~1.1 B logit tokens and applied \"fusion‑merging\" plus DPO alignment, which pushed scores past Arcee‑Nova 2024 and many 40 B‑plus peers on MMLU‑Pro, MATH and HumanEval. With a 128 k context and aggressive quantization options (from BF16 down to 4‑bit GGUF), it balances capability with deployability on single‑GPU nodes. Typical use cases include enterprise chat assistants, technical writing aids and medium‑complexity code drafting where Virtuoso‑Large would be overkill. ", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "arcee-ai", "profile_image_url": "/static/favicon.png"}}, "context_length": 131072, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "command", "object": "model", "created": **********, "owned_by": "cohere", "name": "Cohere: Command", "openai": {"id": "command", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Command is an instruction-following conversational model that performs language tasks with high quality, more reliably and with a longer context than our base generative models.\n\nUse of this model is subject to Cohere's [Usage Policy](https://docs.cohere.com/docs/usage-policy) and [SaaS Agreement](https://cohere.com/saas-agreement).", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "cohere", "profile_image_url": "/static/cohere.png"}}, "context_length": 4096, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "command-a", "object": "model", "created": **********, "owned_by": "cohere", "name": "Cohere: Command A", "openai": {"id": "command-a", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Command A is an open-weights 111B parameter model with a 256k context window focused on delivering great performance across agentic, multilingual, and coding use cases.\nCompared to other leading proprietary and open-weights models Command A delivers maximum performance with minimum hardware costs, excelling on business-critical agentic and multilingual tasks.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "cohere", "profile_image_url": "/static/cohere.png"}}, "context_length": 256000, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "command-r", "object": "model", "created": **********, "owned_by": "cohere", "name": "Cohere: Command R", "openai": {"id": "command-r", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Command-R is a 35B parameter model that performs conversational language tasks at a higher quality, more reliably, and with a longer context than previous models. It can be used for complex workflows like code generation, retrieval augmented generation (RAG), tool use, and agents.\n\nRead the launch post [here](https://txt.cohere.com/command-r/).\n\nUse of this model is subject to Cohere's [Usage Policy](https://docs.cohere.com/docs/usage-policy) and [SaaS Agreement](https://cohere.com/saas-agreement).", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "cohere", "profile_image_url": "/static/cohere.png"}}, "context_length": 128000, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "command-r-plus", "object": "model", "created": **********, "owned_by": "cohere", "name": "Cohere: Command R+", "openai": {"id": "command-r-plus", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Command R+ is a new, 104B-parameter LLM from Cohere. It's useful for roleplay, general consumer usecases, and Retrieval Augmented Generation (RAG).\n\nIt offers multilingual support for ten key languages to facilitate global business operations. See benchmarks and the launch post [here](https://txt.cohere.com/command-r-plus-microsoft-azure/).\n\nUse of this model is subject to Cohere's [Usage Policy](https://docs.cohere.com/docs/usage-policy) and [SaaS Agreement](https://cohere.com/saas-agreement).", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "cohere", "profile_image_url": "/static/cohere.png"}}, "context_length": 128000, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "deepseek-prover-v2", "object": "model", "created": **********, "owned_by": "deepseek", "name": "DeepSeek: DeepSeek Prover V2", "openai": {"id": "deepseek-prover-v2", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "DeepSeek Prover V2 is a 671B parameter model, speculated to be geared towards logic and mathematics. Likely an upgrade from [DeepSeek-Prover-V1.5](https://huggingface.co/deepseek-ai/DeepSeek-Prover-V1.5-RL) Not much is known about the model yet, as DeepSeek released it on Hugging Face without an announcement or description.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "deepseek", "profile_image_url": "/static/deepseek.png"}}, "context_length": 160000, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "deepseek-chat", "object": "model", "created": **********, "owned_by": "deepseek", "name": "DeepSeek: DeepSeek V3", "openai": {"id": "deepseek-chat", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "DeepSeek-V3 is the latest model from the DeepSeek team, building upon the instruction following and coding abilities of the previous versions. Pre-trained on nearly 15 trillion tokens, the reported evaluations reveal that the model outperforms other open-source models and rivals leading closed-source models.\n\nFor model details, please visit [the DeepSeek-V3 repo](https://github.com/deepseek-ai/DeepSeek-V3) for more information, or see the [launch announcement](https://api-docs.deepseek.com/news/news1226).", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "deepseek", "profile_image_url": "/static/deepseek.png"}}, "context_length": 163840, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "deepseek-r1", "object": "model", "created": **********, "owned_by": "deepseek", "name": "DeepSeek: R1", "openai": {"id": "deepseek-r1", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "DeepSeek R1 is here: Performance on par with [OpenAI o1](/openai/o1), but open-sourced and with fully open reasoning tokens. It's 671B parameters in size, with 37B active in an inference pass.\n\nFully open-source model & [technical report](https://api-docs.deepseek.com/news/news250120).\n\nMIT licensed: Distill & commercialize freely!", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "deepseek", "profile_image_url": "/static/deepseek.png"}}, "context_length": 163840, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "deepseek-r1-distill-llama-8b", "object": "model", "created": **********, "owned_by": "deepseek", "name": "DeepSeek: R1 Distill Llama 8B", "openai": {"id": "deepseek-r1-distill-llama-8b", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "DeepSeek R1 Distill Llama 8B is a distilled large language model based on [Llama-3.1-8B-Instruct](/meta-llama/llama-3.1-8b-instruct), using outputs from [DeepSeek R1](/deepseek/deepseek-r1). The model combines advanced distillation techniques to achieve high performance across multiple benchmarks, including:\n\n- AIME 2024 pass@1: 50.4\n- MATH-500 pass@1: 89.1\n- CodeForces Rating: 1205\n\nThe model leverages fine-tuning from DeepSeek R1's outputs, enabling competitive performance comparable to larger frontier models.\n\nHugging Face: \n- [Llama-3.1-8B](https://huggingface.co/meta-llama/Llama-3.1-8B) \n- [DeepSeek-R1-Distill-Llama-8B](https://huggingface.co/deepseek-ai/DeepSeek-R1-Distill-Llama-8B)   |", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "deepseek", "profile_image_url": "/static/deepseek.png"}}, "context_length": 32000, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "deepseek-r1-distill-qwen-14b", "object": "model", "created": **********, "owned_by": "deepseek", "name": "DeepSeek: <PERSON>1 <PERSON><PERSON><PERSON> 14B", "openai": {"id": "deepseek-r1-distill-qwen-14b", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "DeepSeek R1 Distill Qwen 14B is a distilled large language model based on [Qwen 2.5 14B](https://huggingface.co/deepseek-ai/DeepSeek-R1-Distill-Qwen-14B), using outputs from [DeepSeek R1](/deepseek/deepseek-r1). It outperforms OpenAI's o1-mini across various benchmarks, achieving new state-of-the-art results for dense models.\n\nOther benchmark results include:\n\n- AIME 2024 pass@1: 69.7\n- MATH-500 pass@1: 93.9\n- CodeForces Rating: 1481\n\nThe model leverages fine-tuning from DeepSeek R1's outputs, enabling competitive performance comparable to larger frontier models.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "deepseek", "profile_image_url": "/static/deepseek.png"}}, "context_length": 64000, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "llemma_7b", "object": "model", "created": **********, "owned_by": "eleutherai", "name": "EleutherAI: Llemma 7b", "openai": {"id": "llemma_7b", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Llemma 7B is a language model for mathematics. It was initialized with Code Llama 7B weights, and trained on the Proof-Pile-2 for 200B tokens. Llemma models are particularly strong at chain-of-thought mathematical reasoning and using computational tools for mathematics, such as Python and formal theorem provers.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "eleutherai", "profile_image_url": "/static/favicon.png"}}, "context_length": 4096, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "goliath-120b", "object": "model", "created": **********, "owned_by": "alpindale", "name": "Goliath 120B", "openai": {"id": "goliath-120b", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "A large LLM created by combining two fine-tuned Llama 70B models into one 120B model. Combines Xwin and Euryale.\n\nCredits to\n- [@chargoddard](https://huggingface.co/chargoddard) for developing the framework used to merge the model - [mergekit](https://github.com/cg123/mergekit).\n- [@Undi95](https://huggingface.co/Undi95) for helping with the merge ratios.\n\n#merge", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "alpindale", "profile_image_url": "/static/favicon.png"}}, "context_length": 6144, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "gemini-flash-1.5", "object": "model", "created": **********, "owned_by": "google", "name": "Google: Gemini 1.5 Flash", "openai": {"id": "gemini-flash-1.5", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Gemini 1.5 Flash is a foundation model that performs well at a variety of multimodal tasks such as visual understanding, classification, summarization, and creating content from image, audio and video. It's adept at processing visual and text inputs such as photographs, documents, infographics, and screenshots.\n\nGemini 1.5 Flash is designed for high-volume, high-frequency tasks where cost and latency matter. On most common tasks, Flash achieves comparable quality to other Gemini Pro models at a significantly reduced cost. Flash is well-suited for applications like chat assistants and on-demand content generation where speed and scale matter.\n\nUsage of Gemini is subject to Google's [Gemini Terms of Use](https://ai.google.dev/terms).\n\n#multimodal", "modalities": {"input": ["text", "image"], "output": ["text"]}, "provider": "google", "profile_image_url": "/static/google.png"}}, "context_length": 1000000, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "gemini-pro-1.5", "object": "model", "created": **********, "owned_by": "google", "name": "Google: Gemini 1.5 Pro", "openai": {"id": "gemini-pro-1.5", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Google's latest multimodal model, supports image and video[0] in text or chat prompts.\n\nOptimized for language tasks including:\n\n- Code generation\n- Text generation\n- Text editing\n- Problem solving\n- Recommendations\n- Information extraction\n- Data extraction or generation\n- AI agents\n\nUsage of Gemini is subject to Google's [Gemini Terms of Use](https://ai.google.dev/terms).\n\n* [0]: Video input is not available through OpenRouter at this time.", "modalities": {"input": ["text", "image"], "output": ["text"]}, "provider": "google", "profile_image_url": "/static/google.png"}}, "context_length": 2000000, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "gemini-2.5-flash-preview:thinking", "object": "model", "created": **********, "owned_by": "google", "name": "Google: Gemini 2.5 Flash (thinking)", "openai": {"id": "gemini-2.5-flash-preview:thinking", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Gemini 2.5 Flash is Google's state-of-the-art workhorse model, specifically designed for advanced reasoning, coding, mathematics, and scientific tasks. It includes built-in \"thinking\" capabilities, enabling it to provide responses with greater accuracy and nuanced context handling. \n\nNote: This model is available in two variants: thinking and non-thinking. The output pricing varies significantly depending on whether the thinking capability is active. If you select the standard variant (without the \":thinking\" suffix), the model will explicitly avoid generating thinking tokens. \n\nTo utilize the thinking capability and receive thinking tokens, you must choose the \":thinking\" variant, which will then incur the higher thinking-output pricing. \n\nAdditionally, Gemini 2.5 Flash is configurable through the \"max tokens for reasoning\" parameter, as described in the documentation (https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning).", "modalities": {"input": ["image", "text", "file"], "output": ["text"]}, "provider": "google", "profile_image_url": "/static/google.png"}}, "context_length": 1048576, "reasoning_level": "light", "auto_select_priority": 0, "premium_model": false, "actions": [], "tags": []}, {"id": "gemini-2.5-flash-preview", "object": "model", "created": **********, "owned_by": "google", "name": "Google: Gemini 2.5 Flash Preview", "openai": {"id": "gemini-2.5-flash-preview", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Gemini 2.5 Flash is Google's state-of-the-art workhorse model, specifically designed for advanced reasoning, coding, mathematics, and scientific tasks. It includes built-in \"thinking\" capabilities, enabling it to provide responses with greater accuracy and nuanced context handling. \n\nNote: This model is available in two variants: thinking and non-thinking. The output pricing varies significantly depending on whether the thinking capability is active. If you select the standard variant (without the \":thinking\" suffix), the model will explicitly avoid generating thinking tokens. \n\nTo utilize the thinking capability and receive thinking tokens, you must choose the \":thinking\" variant, which will then incur the higher thinking-output pricing. \n\nAdditionally, Gemini 2.5 Flash is configurable through the \"max tokens for reasoning\" parameter, as described in the documentation (https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning).", "modalities": {"input": ["image", "text", "file"], "output": ["text"]}, "provider": "google", "profile_image_url": "/static/google.png"}}, "context_length": 1048576, "reasoning_level": "fast", "auto_select_priority": 0, "premium_model": false, "actions": [], "tags": []}, {"id": "gemini-2.5-flash-preview-05-20", "object": "model", "created": **********, "owned_by": "google", "name": "Google: Gemini 2.5 Flash Preview 05-20", "openai": {"id": "gemini-2.5-flash-preview-05-20", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Gemini 2.5 Flash May 20th Checkpoint is Google's state-of-the-art workhorse model, specifically designed for advanced reasoning, coding, mathematics, and scientific tasks. It includes built-in \"thinking\" capabilities, enabling it to provide responses with greater accuracy and nuanced context handling. \n\nNote: This model is available in two variants: thinking and non-thinking. The output pricing varies significantly depending on whether the thinking capability is active. If you select the standard variant (without the \":thinking\" suffix), the model will explicitly avoid generating thinking tokens. \n\nTo utilize the thinking capability and receive thinking tokens, you must choose the \":thinking\" variant, which will then incur the higher thinking-output pricing. \n\nAdditionally, Gemini 2.5 Flash is configurable through the \"max tokens for reasoning\" parameter, as described in the documentation (https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning).", "modalities": {"input": ["image", "text", "file"], "output": ["text"]}, "provider": "google", "profile_image_url": "/static/google.png"}}, "context_length": 1048576, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "gemini-2.5-flash-preview-05-20:thinking", "object": "model", "created": **********, "owned_by": "google", "name": "Google: Gemini 2.5 Flash Preview 05-20 (thinking)", "openai": {"id": "gemini-2.5-flash-preview-05-20:thinking", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Gemini 2.5 Flash May 20th Checkpoint is Google's state-of-the-art workhorse model, specifically designed for advanced reasoning, coding, mathematics, and scientific tasks. It includes built-in \"thinking\" capabilities, enabling it to provide responses with greater accuracy and nuanced context handling. \n\nNote: This model is available in two variants: thinking and non-thinking. The output pricing varies significantly depending on whether the thinking capability is active. If you select the standard variant (without the \":thinking\" suffix), the model will explicitly avoid generating thinking tokens. \n\nTo utilize the thinking capability and receive thinking tokens, you must choose the \":thinking\" variant, which will then incur the higher thinking-output pricing. \n\nAdditionally, Gemini 2.5 Flash is configurable through the \"max tokens for reasoning\" parameter, as described in the documentation (https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning).", "modalities": {"input": ["image", "text", "file"], "output": ["text"]}, "provider": "google", "profile_image_url": "/static/google.png"}}, "context_length": 1048576, "reasoning_level": "heavy", "premium_model": false, "actions": [], "tags": []}, {"id": "gemini-2.5-pro-preview", "object": "model", "created": **********, "owned_by": "google", "name": "Google: Gemini 2.5 Pro Preview 06-05", "openai": {"id": "gemini-2.5-pro-preview", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Gemini 2.5 Pro is Google’s state-of-the-art AI model designed for advanced reasoning, coding, mathematics, and scientific tasks. It employs “thinking” capabilities, enabling it to reason through responses with enhanced accuracy and nuanced context handling. Gemini 2.5 Pro achieves top-tier performance on multiple benchmarks, including first-place positioning on the LMArena leaderboard, reflecting superior human-preference alignment and complex problem-solving abilities.\n", "modalities": {"input": ["file", "image", "text"], "output": ["text"]}, "provider": "google", "profile_image_url": "/static/google.png"}}, "context_length": 1048576, "reasoning_level": "heavy", "premium_model": false, "actions": [], "tags": []}, {"id": "gemma-2b-it", "object": "model", "created": **********, "owned_by": "google", "name": "Google: Gemma 1 2B", "openai": {"id": "gemma-2b-it", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Gemma 1 2B by Google is an open model built from the same research and technology used to create the [Gemini models](/models?q=gemini).\n\nGemma models are well-suited for a variety of text generation tasks, including question answering, summarization, and reasoning.\n\nUsage of Gemma is subject to Google's [Gemma Terms of Use](https://ai.google.dev/gemma/terms).", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "google", "profile_image_url": "/static/google.png"}}, "context_length": 8192, "reasoning_level": "light", "premium_model": false, "actions": [], "tags": []}, {"id": "gemma-3-12b-it", "object": "model", "created": **********, "owned_by": "google", "name": "Google: Gemma 3 12B", "openai": {"id": "gemma-3-12b-it", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Gemma 3 introduces multimodality, supporting vision-language input and text outputs. It handles context windows up to 128k tokens, understands over 140 languages, and offers improved math, reasoning, and chat capabilities, including structured outputs and function calling. Gemma 3 12B is the second largest in the family of Gemma 3 models after [Gemma 3 27B](google/gemma-3-27b-it)", "modalities": {"input": ["text", "image"], "output": ["text"]}, "provider": "google", "profile_image_url": "/static/google.png"}}, "context_length": 131072, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "gemma-3-27b-it", "object": "model", "created": **********, "owned_by": "google", "name": "Google: Gemma 3 27B", "openai": {"id": "gemma-3-27b-it", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Gemma 3 introduces multimodality, supporting vision-language input and text outputs. It handles context windows up to 128k tokens, understands over 140 languages, and offers improved math, reasoning, and chat capabilities, including structured outputs and function calling. Gemma 3 27B is Google's latest open source model, successor to [Gemma 2](google/gemma-2-27b-it)", "modalities": {"input": ["text", "image"], "output": ["text"]}, "provider": "google", "profile_image_url": "/static/google.png"}}, "context_length": 131072, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "gemma-3-4b-it", "object": "model", "created": **********, "owned_by": "google", "name": "Google: Gemma 3 4B", "openai": {"id": "gemma-3-4b-it", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Gemma 3 introduces multimodality, supporting vision-language input and text outputs. It handles context windows up to 128k tokens, understands over 140 languages, and offers improved math, reasoning, and chat capabilities, including structured outputs and function calling.", "modalities": {"input": ["text", "image"], "output": ["text"]}, "provider": "google", "profile_image_url": "/static/google.png"}}, "context_length": 131072, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "mercury-coder-small-beta", "object": "model", "created": **********, "owned_by": "inception", "name": "Inception: Mercury Coder Small Beta", "openai": {"id": "mercury-coder-small-beta", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Mercury Coder Small is the first diffusion large language model (dLLM). Applying a breakthrough discrete diffusion approach, the model runs 5-10x faster than even speed optimized models like Claude 3.5 Haiku and GPT-4o Mini while matching their performance. Mercury Coder Small's speed means that developers can stay in the flow while coding, enjoying rapid chat-based iteration and responsive code completion suggestions. On Copilot Arena, Mercury Coder ranks 1st in speed and ties for 2nd in quality. Read more in the [blog post here](https://www.inceptionlabs.ai/introducing-mercury).", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "inception", "profile_image_url": "/static/favicon.png"}}, "context_length": 32000, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "inflection-3-pi", "object": "model", "created": **********, "owned_by": "inflection", "name": "Inflection: Inflection 3 Pi", "openai": {"id": "inflection-3-pi", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Inflection 3 Pi powers Inflection's [<PERSON>](https://pi.ai) chatbot, including backstory, emotional intelligence, productivity, and safety. It has access to recent news, and excels in scenarios like customer support and roleplay.\n\n<PERSON> has been trained to mirror your tone and style, if you use more emojis, so will Pi! Try experimenting with various prompts and conversation styles.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "inflection", "profile_image_url": "/static/favicon.png"}}, "context_length": 8000, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "inflection-3-productivity", "object": "model", "created": **********, "owned_by": "inflection", "name": "Inflection: Inflection 3 Productivity", "openai": {"id": "inflection-3-productivity", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Inflection 3 Productivity is optimized for following instructions. It is better for tasks requiring JSON output or precise adherence to provided guidelines. It has access to recent news.\n\nFor emotional intelligence similar to Pi, see [Inflect 3 Pi](/inflection/inflection-3-pi)\n\nSee [Inflection's announcement](https://inflection.ai/blog/enterprise) for more details.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "inflection", "profile_image_url": "/static/favicon.png"}}, "context_length": 8000, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "lfm-3b", "object": "model", "created": **********, "owned_by": "liquid", "name": "Liquid: LFM 3B", "openai": {"id": "lfm-3b", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Liquid's LFM 3B delivers incredible performance for its size. It positions itself as first place among 3B parameter transformers, hybrids, and RNN models It is also on par with Phi-3.5-mini on multiple benchmarks, while being 18.4% smaller.\n\nLFM-3B is the ideal choice for mobile and other edge text-based applications.\n\nSee the [launch announcement](https://www.liquid.ai/liquid-foundation-models) for benchmarks and more info.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "liquid", "profile_image_url": "/static/favicon.png"}}, "context_length": 32768, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "lfm-40b", "object": "model", "created": **********, "owned_by": "liquid", "name": "Liquid: LFM 40B MoE", "openai": {"id": "lfm-40b", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Liquid's 40.3B Mixture of Experts (MoE) model. Liquid Foundation Models (LFMs) are large neural networks built with computational units rooted in dynamic systems.\n\nLFMs are general-purpose AI models that can be used to model any kind of sequential data, including video, audio, text, time series, and signals.\n\nSee the [launch announcement](https://www.liquid.ai/liquid-foundation-models) for benchmarks and more info.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "liquid", "profile_image_url": "/static/favicon.png"}}, "context_length": 32768, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "lfm-7b", "object": "model", "created": **********, "owned_by": "liquid", "name": "Liquid: LFM 7B", "openai": {"id": "lfm-7b", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "LFM-7B, a new best-in-class language model. LFM-7B is designed for exceptional chat capabilities, including languages like Arabic and Japanese. Powered by the Liquid Foundation Model (LFM) architecture, it exhibits unique features like low memory footprint and fast inference speed. \n\nLFM-7B is the world’s best-in-class multilingual language model in English, Arabic, and Japanese.\n\nSee the [launch announcement](https://www.liquid.ai/lfm-7b) for benchmarks and more info.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "liquid", "profile_image_url": "/static/favicon.png"}}, "context_length": 32768, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "magnum-v2-72b", "object": "model", "created": **********, "owned_by": "anthracite-org", "name": "Magnum v2 72B", "openai": {"id": "magnum-v2-72b", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "From the maker of [Goliath](https://openrouter.ai/models/alpindale/goliath-120b), Magnum 72B is the seventh in a family of models designed to achieve the prose quality of the Claude 3 models, notably Opus & Sonnet.\n\nThe model is based on [Qwen2 72B](https://openrouter.ai/models/qwen/qwen-2-72b-instruct) and trained with 55 million tokens of highly curated roleplay (RP) data.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "anthracite-org", "profile_image_url": "/static/favicon.png"}}, "context_length": 32768, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "magnum-v4-72b", "object": "model", "created": **********, "owned_by": "anthracite-org", "name": "Magnum v4 72B", "openai": {"id": "magnum-v4-72b", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "This is a series of models designed to replicate the prose quality of the Claude 3 models, specifically Sonnet(https://openrouter.ai/anthropic/claude-3.5-sonnet) and Opus(https://openrouter.ai/anthropic/claude-3-opus).\n\nThe model is fine-tuned on top of [Qwen2.5 72B](https://openrouter.ai/qwen/qwen-2.5-72b-instruct).", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "anthracite-org", "profile_image_url": "/static/favicon.png"}}, "context_length": 16384, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "llama-3.3-70b-instruct", "object": "model", "created": **********, "owned_by": "meta-llama", "name": "Meta: Llama 3.3 70B Instruct", "openai": {"id": "llama-3.3-70b-instruct", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "The Meta Llama 3.3 multilingual large language model (LLM) is a pretrained and instruction tuned generative model in 70B (text in/text out). The Llama 3.3 instruction tuned text only model is optimized for multilingual dialogue use cases and outperforms many of the available open source and closed chat models on common industry benchmarks.\n\nSupported languages: English, German, French, Italian, Portuguese, Hindi, Spanish, and Thai.\n\n[Model Card](https://github.com/meta-llama/llama-models/blob/main/models/llama3_3/MODEL_CARD.md)", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "meta-llama", "profile_image_url": "/static/meta.png"}}, "context_length": 131072, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "llama-4-scout", "object": "model", "created": **********, "owned_by": "meta-llama", "name": "Meta: Llama 4 Scout", "openai": {"id": "llama-4-scout", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Llama 4 Scout 17B Instruct (16E) is a mixture-of-experts (MoE) language model developed by Meta, activating 17 billion parameters out of a total of 109B. It supports native multimodal input (text and image) and multilingual output (text and code) across 12 supported languages. Designed for assistant-style interaction and visual reasoning, <PERSON> uses 16 experts per forward pass and features a context length of 10 million tokens, with a training corpus of ~40 trillion tokens.\n\nBuilt for high efficiency and local or commercial deployment, Llama 4 Scout incorporates early fusion for seamless modality integration. It is instruction-tuned for use in multilingual chat, captioning, and image understanding tasks. Released under the Llama 4 Community License, it was last trained on data up to August 2024 and launched publicly on April 5, 2025.", "modalities": {"input": ["text", "image"], "output": ["text"]}, "provider": "meta-llama", "profile_image_url": "/static/meta.png"}}, "context_length": 172000, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "phi-4", "object": "model", "created": **********, "owned_by": "microsoft", "name": "Microsoft: Phi 4", "openai": {"id": "phi-4", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "[Microsoft Research](/microsoft) Phi-4 is designed to perform well in complex reasoning tasks and can operate efficiently in situations with limited memory or where quick responses are needed. \n\nAt 14 billion parameters, it was trained on a mix of high-quality synthetic datasets, data from curated websites, and academic materials. It has undergone careful improvement to follow instructions accurately and maintain strong safety standards. It works best with English language inputs.\n\nFor more information, please see [Phi-4 Technical Report](https://arxiv.org/pdf/2412.08905)\n", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "microsoft", "profile_image_url": "/static/microsoft.png"}}, "context_length": 16384, "reasoning_level": "light", "premium_model": false, "actions": [], "tags": []}, {"id": "phi-4-multimodal-instruct", "object": "model", "created": **********, "owned_by": "microsoft", "name": "Microsoft: Phi 4 Multimodal Instruct", "openai": {"id": "phi-4-multimodal-instruct", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Phi-4 Multimodal Instruct is a versatile 5.6B parameter foundation model that combines advanced reasoning and instruction-following capabilities across both text and visual inputs, providing accurate text outputs. The unified architecture enables efficient, low-latency inference, suitable for edge and mobile deployments. Phi-4 Multimodal Instruct supports text inputs in multiple languages including Arabic, Chinese, English, French, German, Japanese, Spanish, and more, with visual input optimized primarily for English. It delivers impressive performance on multimodal tasks involving mathematical, scientific, and document reasoning, providing developers and enterprises a powerful yet compact model for sophisticated interactive applications. For more information, see the [Phi-4 Multimodal blog post](https://azure.microsoft.com/en-us/blog/empowering-innovation-the-next-generation-of-the-phi-family/).\n", "modalities": {"input": ["text", "image"], "output": ["text"]}, "provider": "microsoft", "profile_image_url": "/static/microsoft.png"}}, "context_length": 131072, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "phi-4-reasoning-plus", "object": "model", "created": **********, "owned_by": "microsoft", "name": "Microsoft: Phi 4 Reasoning Plus", "openai": {"id": "phi-4-reasoning-plus", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Phi-4-reasoning-plus is an enhanced 14B parameter model from Microsoft, fine-tuned from Phi-4 with additional reinforcement learning to boost accuracy on math, science, and code reasoning tasks. It uses the same dense decoder-only transformer architecture as Phi-4, but generates longer, more comprehensive outputs structured into a step-by-step reasoning trace and final answer.\n\nWhile it offers improved benchmark scores over Phi-4-reasoning across tasks like AIME, OmniMath, and HumanEvalPlus, its responses are typically ~50% longer, resulting in higher latency. Designed for English-only applications, it is well-suited for structured reasoning workflows where output quality takes priority over response speed.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "microsoft", "profile_image_url": "/static/microsoft.png"}}, "context_length": 32768, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "phi-3-medium-128k-instruct", "object": "model", "created": **********, "owned_by": "microsoft", "name": "Microsoft: Phi-3 Medium 128K Instruct", "openai": {"id": "phi-3-medium-128k-instruct", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Phi-3 128K Medium is a powerful 14-billion parameter model designed for advanced language understanding, reasoning, and instruction following. Optimized through supervised fine-tuning and preference adjustments, it excels in tasks involving common sense, mathematics, logical reasoning, and code processing.\n\nAt time of release, Phi-3 Medium demonstrated state-of-the-art performance among lightweight models. In the MMLU-Pro eval, the model even comes close to a Llama3 70B level of performance.\n\nFor 4k context length, try [Phi-3 Medium 4K](/models/microsoft/phi-3-medium-4k-instruct).", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "microsoft", "profile_image_url": "/static/microsoft.png"}}, "context_length": 128000, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "phi-3.5-mini-128k-instruct", "object": "model", "created": **********, "owned_by": "microsoft", "name": "Microsoft: Phi-3.5 Mini 128K Instruct", "openai": {"id": "phi-3.5-mini-128k-instruct", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Phi-3.5 models are lightweight, state-of-the-art open models. These models were trained with Phi-3 datasets that include both synthetic data and the filtered, publicly available websites data, with a focus on high quality and reasoning-dense properties. Phi-3.5 Mini uses 3.8B parameters, and is a dense decoder-only transformer model using the same tokenizer as [Phi-3 Mini](/models/microsoft/phi-3-mini-128k-instruct).\n\nThe models underwent a rigorous enhancement process, incorporating both supervised fine-tuning, proximal policy optimization, and direct preference optimization to ensure precise instruction adherence and robust safety measures. When assessed against benchmarks that test common sense, language understanding, math, code, long context and logical reasoning, Phi-3.5 models showcased robust and state-of-the-art performance among models with less than 13 billion parameters.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "microsoft", "profile_image_url": "/static/microsoft.png"}}, "context_length": 128000, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "mistral-large", "object": "model", "created": **********, "owned_by": "mist<PERSON><PERSON>", "name": "Mistral Large", "openai": {"id": "mistral-large", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "This is Mistral AI's flagship model, Mistral Large 2 (version `mistral-large-2407`). It's a proprietary weights-available model and excels at reasoning, code, JSON, chat, and more. Read the launch announcement [here](https://mistral.ai/news/mistral-large-2407/).\n\nIt supports dozens of languages including French, German, Spanish, Italian, Portuguese, Arabic, Hindi, Russian, Chinese, Japanese, and Korean, along with 80+ coding languages including Python, Java, C, C++, JavaScript, and Bash. Its long context window allows precise information recall from large documents.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "mist<PERSON><PERSON>", "profile_image_url": "/static/favicon.png"}}, "context_length": 128000, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "mistral-medium", "object": "model", "created": **********, "owned_by": "mist<PERSON><PERSON>", "name": "Mistral Medium", "openai": {"id": "mistral-medium", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "This is Mistral AI's closed-source, medium-sided model. It's powered by a closed-source prototype and excels at reasoning, code, JSON, chat, and more. In benchmarks, it compares with many of the flagship models of other companies.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "mist<PERSON><PERSON>", "profile_image_url": "/static/favicon.png"}}, "context_length": 32768, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "mistral-small", "object": "model", "created": **********, "owned_by": "mist<PERSON><PERSON>", "name": "Mistra<PERSON> Small", "openai": {"id": "mistral-small", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "With 22 billion parameters, Mistral Small v24.09 offers a convenient mid-point between (Mistral NeMo 12B)[/mistralai/mistral-nemo] and (Mistral Large 2)[/mistralai/mistral-large], providing a cost-effective solution that can be deployed across various platforms and environments. It has better reasoning, exhibits more capabilities, can produce and reason about code, and is multiligual, supporting English, French, German, Italian, and Spanish.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "mist<PERSON><PERSON>", "profile_image_url": "/static/favicon.png"}}, "context_length": 32768, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "mistral-tiny", "object": "model", "created": **********, "owned_by": "mist<PERSON><PERSON>", "name": "Mistral Tiny", "openai": {"id": "mistral-tiny", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Note: This model is being deprecated. Recommended replacement is the newer [Ministral 8B](/mistral/ministral-8b)\n\nThis model is currently powered by Mistral-7B-v0.2, and incorporates a \"better\" fine-tuning than [Mistral 7B](/models/mistralai/mistral-7b-instruct-v0.1), inspired by community work. It's best used for large batch processing tasks where cost is a significant factor but reasoning capabilities are not crucial.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "mist<PERSON><PERSON>", "profile_image_url": "/static/favicon.png"}}, "context_length": 32768, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "codestral-2501", "object": "model", "created": **********, "owned_by": "mist<PERSON><PERSON>", "name": "Mistral: Codestral 2501", "openai": {"id": "codestral-2501", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "[Mistral](/mistralai)'s cutting-edge language model for coding. Codestral specializes in low-latency, high-frequency tasks such as fill-in-the-middle (FIM), code correction and test generation. \n\nLearn more on their blog post: https://mistral.ai/news/codestral-2501/", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "mist<PERSON><PERSON>", "profile_image_url": "/static/favicon.png"}}, "context_length": 262144, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "devstral-small", "object": "model", "created": **********, "owned_by": "mist<PERSON><PERSON>", "name": "Mistral: <PERSON><PERSON><PERSON> Small", "openai": {"id": "devstral-small", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Devstral-Small-2505 is a 24B parameter agentic LLM fine-tuned from Mistral-Small-3.1, jointly developed by Mistral AI and All Hands AI for advanced software engineering tasks. It is optimized for codebase exploration, multi-file editing, and integration into coding agents, achieving state-of-the-art results on SWE-Bench Verified (46.8%).\n\nDevstral supports a 128k context window and uses a custom Tekken tokenizer. It is text-only, with the vision encoder removed, and is suitable for local deployment on high-end consumer hardware (e.g., RTX 4090, 32GB RAM Macs). Devstral is best used in agentic workflows via the OpenHands scaffold and is compatible with inference frameworks like vLLM, Transformers, and Ollama. It is released under the Apache 2.0 license.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "mist<PERSON><PERSON>", "profile_image_url": "/static/favicon.png"}}, "context_length": 131072, "reasoning_level": "light", "premium_model": false, "actions": [], "tags": []}, {"id": "ministral-3b", "object": "model", "created": **********, "owned_by": "mist<PERSON><PERSON>", "name": "Mistral: Ministral 3B", "openai": {"id": "ministral-3b", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Ministral 3B is a 3B parameter model optimized for on-device and edge computing. It excels in knowledge, commonsense reasoning, and function-calling, outperforming larger models like Mistral 7B on most benchmarks. Supporting up to 128k context length, it’s ideal for orchestrating agentic workflows and specialist tasks with efficient inference.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "mist<PERSON><PERSON>", "profile_image_url": "/static/favicon.png"}}, "context_length": 131072, "reasoning_level": "light", "premium_model": false, "actions": [], "tags": []}, {"id": "ministral-8b", "object": "model", "created": **********, "owned_by": "mist<PERSON><PERSON>", "name": "Mistral: Ministral 8B", "openai": {"id": "ministral-8b", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Ministral 8B is an 8B parameter model featuring a unique interleaved sliding-window attention pattern for faster, memory-efficient inference. Designed for edge use cases, it supports up to 128k context length and excels in knowledge and reasoning tasks. It outperforms peers in the sub-10B category, making it perfect for low-latency, privacy-first applications.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "mist<PERSON><PERSON>", "profile_image_url": "/static/favicon.png"}}, "context_length": 128000, "reasoning_level": "light", "premium_model": false, "actions": [], "tags": []}, {"id": "mistral-7b-instruct", "object": "model", "created": **********, "owned_by": "mist<PERSON><PERSON>", "name": "Mistral: Mistral 7B Instruct", "openai": {"id": "mistral-7b-instruct", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "A high-performing, industry-standard 7.3B parameter model, with optimizations for speed and context length.\n\n*Mistral 7B Instruct has multiple version variants, and this is intended to be the latest version.*", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "mist<PERSON><PERSON>", "profile_image_url": "/static/favicon.png"}}, "context_length": 32768, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "mistral-medium-3", "object": "model", "created": **********, "owned_by": "mist<PERSON><PERSON>", "name": "Mistral: Mistral Medium 3", "openai": {"id": "mistral-medium-3", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Mistral Medium 3 is a high-performance enterprise-grade language model designed to deliver frontier-level capabilities at significantly reduced operational cost. It balances state-of-the-art reasoning and multimodal performance with 8× lower cost compared to traditional large models, making it suitable for scalable deployments across professional and industrial use cases.\n\nThe model excels in domains such as coding, STEM reasoning, and enterprise adaptation. It supports hybrid, on-prem, and in-VPC deployments and is optimized for integration into custom workflows. Mistral Medium 3 offers competitive accuracy relative to larger models like Claude Sonnet 3.5/3.7, Llama 4 Maverick, and Command R+, while maintaining broad compatibility across cloud environments.", "modalities": {"input": ["text", "image"], "output": ["text"]}, "provider": "mist<PERSON><PERSON>", "profile_image_url": "/static/favicon.png"}}, "context_length": 131072, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "mistral-nemo", "object": "model", "created": **********, "owned_by": "mist<PERSON><PERSON>", "name": "Mistral: <PERSON><PERSON><PERSON> Nemo", "openai": {"id": "mistral-nemo", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "A 12B parameter model with a 128k token context length built by Mistral in collaboration with NVIDIA.\n\nThe model is multilingual, supporting English, French, German, Spanish, Italian, Portuguese, Chinese, Japanese, Korean, Arabic, and Hindi.\n\nIt supports function calling and is released under the Apache 2.0 license.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "mist<PERSON><PERSON>", "profile_image_url": "/static/favicon.png"}}, "context_length": 131072, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "mistral-small-24b-instruct-2501", "object": "model", "created": **********, "owned_by": "mist<PERSON><PERSON>", "name": "Mistral: Mistral Small 3", "openai": {"id": "mistral-small-24b-instruct-2501", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Mistral Small 3 is a 24B-parameter language model optimized for low-latency performance across common AI tasks. Released under the Apache 2.0 license, it features both pre-trained and instruction-tuned versions designed for efficient local deployment.\n\nThe model achieves 81% accuracy on the MMLU benchmark and performs competitively with larger models like Llama 3.3 70B and Qwen 32B, while operating at three times the speed on equivalent hardware. [Read the blog post about the model here.](https://mistral.ai/news/mistral-small-3/)", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "mist<PERSON><PERSON>", "profile_image_url": "/static/favicon.png"}}, "context_length": 32768, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "mistral-small-3.1-24b-instruct", "object": "model", "created": **********, "owned_by": "mist<PERSON><PERSON>", "name": "Mistral: Mistra<PERSON> Small 3.1 24B", "openai": {"id": "mistral-small-3.1-24b-instruct", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Mistral Small 3.1 24B Instruct is an upgraded variant of Mistral Small 3 (2501), featuring 24 billion parameters with advanced multimodal capabilities. It provides state-of-the-art performance in text-based reasoning and vision tasks, including image analysis, programming, mathematical reasoning, and multilingual support across dozens of languages. Equipped with an extensive 128k token context window and optimized for efficient local inference, it supports use cases such as conversational agents, function calling, long-document comprehension, and privacy-sensitive deployments.", "modalities": {"input": ["text", "image"], "output": ["text"]}, "provider": "mist<PERSON><PERSON>", "profile_image_url": "/static/favicon.png"}}, "context_length": 32768, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "mixtral-8x22b-instruct", "object": "model", "created": **********, "owned_by": "mist<PERSON><PERSON>", "name": "Mistral: Mixtral 8x22B Instruct", "openai": {"id": "mixtral-8x22b-instruct", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Mistral's official instruct fine-tuned version of [Mixtral 8x22B](/models/mistralai/mixtral-8x22b). It uses 39B active parameters out of 141B, offering unparalleled cost efficiency for its size. Its strengths include:\n- strong math, coding, and reasoning\n- large context length (64k)\n- fluency in English, French, Italian, German, and Spanish\n\nSee benchmarks on the launch announcement [here](https://mistral.ai/news/mixtral-8x22b/).\n#moe", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "mist<PERSON><PERSON>", "profile_image_url": "/static/favicon.png"}}, "context_length": 65536, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "mixtral-8x7b-instruct", "object": "model", "created": **********, "owned_by": "mist<PERSON><PERSON>", "name": "Mistral: Mixtral 8x7B Instruct", "openai": {"id": "mixtral-8x7b-instruct", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Mixtral 8x7B Instruct is a pretrained generative Sparse Mixture of Experts, by Mistral AI, for chat and instruction use. Incorporates 8 experts (feed-forward networks) for a total of 47 billion parameters.\n\nInstruct model fine-tuned by Mistral. #moe", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "mist<PERSON><PERSON>", "profile_image_url": "/static/favicon.png"}}, "context_length": 32768, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "pixtral-12b", "object": "model", "created": **********, "owned_by": "mist<PERSON><PERSON>", "name": "Mistral: Pixtral 12B", "openai": {"id": "pixtral-12b", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "The first multi-modal, text+image-to-text model from Mistral AI. Its weights were launched via torrent: https://x.com/mistralai/status/1833758285167722836.", "modalities": {"input": ["text", "image"], "output": ["text"]}, "provider": "mist<PERSON><PERSON>", "profile_image_url": "/static/favicon.png"}}, "context_length": 32768, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "pixtral-large-2411", "object": "model", "created": **********, "owned_by": "mist<PERSON><PERSON>", "name": "Mistral: Pixtral Large 2411", "openai": {"id": "pixtral-large-2411", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Pixtral Large is a 124B parameter, open-weight, multimodal model built on top of [Mistral Large 2](/mistralai/mistral-large-2411). The model is able to understand documents, charts and natural images.\n\nThe model is available under the Mistral Research License (MRL) for research and educational use, and the Mistral Commercial License for experimentation, testing, and production for commercial purposes.\n\n", "modalities": {"input": ["text", "image"], "output": ["text"]}, "provider": "mist<PERSON><PERSON>", "profile_image_url": "/static/favicon.png"}}, "context_length": 131072, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "mistral-saba", "object": "model", "created": **********, "owned_by": "mist<PERSON><PERSON>", "name": "Mistral: Sa<PERSON>", "openai": {"id": "mistral-saba", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Mistral Saba is a 24B-parameter language model specifically designed for the Middle East and South Asia, delivering accurate and contextually relevant responses while maintaining efficient performance. Trained on curated regional datasets, it supports multiple Indian-origin languages—including Tamil and Malayalam—alongside Arabic. This makes it a versatile option for a range of regional and multilingual applications. Read more at the blog post [here](https://mistral.ai/en/news/mistral-saba)", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "mist<PERSON><PERSON>", "profile_image_url": "/static/favicon.png"}}, "context_length": 32768, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "llama-3.3-nemotron-super-49b-v1", "object": "model", "created": **********, "owned_by": "nvidia", "name": "NVIDIA: Llama 3.3 Nemotron Super 49B v1", "openai": {"id": "llama-3.3-nemotron-super-49b-v1", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Llama-3.3-Nemotron-Super-49B-v1 is a large language model (LLM) optimized for advanced reasoning, conversational interactions, retrieval-augmented generation (RAG), and tool-calling tasks. Derived from Meta's Llama-3.3-70B-Instruct, it employs a Neural Architecture Search (NAS) approach, significantly enhancing efficiency and reducing memory requirements. This allows the model to support a context length of up to 128K tokens and fit efficiently on single high-performance GPUs, such as NVIDIA H200.\n\nNote: you must include `detailed thinking on` in the system prompt to enable reasoning. Please see [Usage Recommendations](https://huggingface.co/nvidia/Llama-3_1-Nemotron-Ultra-253B-v1#quick-start-and-usage-recommendations) for more.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "nvidia", "profile_image_url": "/static/favicon.png"}}, "context_length": 131072, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "nous-hermes-2-mixtral-8x7b-dpo", "object": "model", "created": **********, "owned_by": "nousresearch", "name": "Nous: Hermes 2 Mixtral 8x7B DPO", "openai": {"id": "nous-hermes-2-mixtral-8x7b-dpo", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Nous Hermes 2 Mixtral 8x7B DPO is the new flagship Nous Research model trained over the [Mixtral 8x7B MoE LLM](/models/mistralai/mixtral-8x7b).\n\nThe model was trained on over 1,000,000 entries of primarily [GPT-4](/models/openai/gpt-4) generated data, as well as other high quality data from open datasets across the AI landscape, achieving state of the art performance on a variety of tasks.\n\n#moe", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "nousresearch", "profile_image_url": "/static/favicon.png"}}, "context_length": 32768, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "gpt-3.5-turbo", "object": "model", "created": **********, "owned_by": "openai", "name": "OpenAI: GPT-3.5 Turbo", "openai": {"id": "gpt-3.5-turbo", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "GPT-3.5 Turbo is OpenAI's fastest model. It can understand and generate natural language or code, and is optimized for chat and traditional completion tasks.\n\nTraining data up to Sep 2021.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "openai", "profile_image_url": "/static/openai.png"}}, "context_length": 16385, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "gpt-4", "object": "model", "created": **********, "owned_by": "openai", "name": "OpenAI: GPT-4", "openai": {"id": "gpt-4", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "OpenAI's flagship model, GPT-4 is a large-scale multimodal language model capable of solving difficult problems with greater accuracy than previous models due to its broader general knowledge and advanced reasoning capabilities. Training data: up to Sep 2021.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "openai", "profile_image_url": "/static/openai.png"}}, "context_length": 8191, "reasoning_level": "fast", "premium_model": true, "actions": [], "tags": []}, {"id": "gpt-4-turbo", "object": "model", "created": **********, "owned_by": "openai", "name": "OpenAI: GPT-4 Turbo", "openai": {"id": "gpt-4-turbo", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "The latest GPT-4 Turbo model with vision capabilities. Vision requests can now use JSON mode and function calling.\n\nTraining data: up to December 2023.", "modalities": {"input": ["text", "image"], "output": ["text"]}, "provider": "openai", "profile_image_url": "/static/openai.png"}}, "context_length": 128000, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "gpt-4-turbo-preview", "object": "model", "created": **********, "owned_by": "openai", "name": "OpenAI: GPT-4 Turbo Preview", "openai": {"id": "gpt-4-turbo-preview", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "The preview GPT-4 model with improved instruction following, JSON mode, reproducible outputs, parallel function calling, and more. Training data: up to Dec 2023.\n\n**Note:** heavily rate limited by OpenAI while in preview.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "openai", "profile_image_url": "/static/openai.png"}}, "context_length": 128000, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "gpt-4.1", "object": "model", "created": **********, "owned_by": "openai", "name": "OpenAI: GPT-4.1", "openai": {"id": "gpt-4.1", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "GPT-4.1 is a flagship large language model optimized for advanced instruction following, real-world software engineering, and long-context reasoning. It supports a 1 million token context window and outperforms GPT-4o and GPT-4.5 across coding (54.6% SWE-bench Verified), instruction compliance (87.4% IFEval), and multimodal understanding benchmarks. It is tuned for precise code diffs, agent reliability, and high recall in large document contexts, making it ideal for agents, IDE tooling, and enterprise knowledge retrieval.", "modalities": {"input": ["image", "text", "file"], "output": ["text"]}, "provider": "openai", "profile_image_url": "/static/openai.png"}}, "context_length": 1047576, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "gpt-4.1-mini", "object": "model", "created": **********, "owned_by": "openai", "name": "OpenAI: GPT-4.1 Mini", "openai": {"id": "gpt-4.1-mini", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "GPT-4.1 Mini is a mid-sized model delivering performance competitive with GPT-4o at substantially lower latency and cost. It retains a 1 million token context window and scores 45.1% on hard instruction evals, 35.8% on MultiChallenge, and 84.1% on IFEval. Mini also shows strong coding ability (e.g., 31.6% on Aid<PERSON>’s polyglot diff benchmark) and vision understanding, making it suitable for interactive applications with tight performance constraints.", "modalities": {"input": ["image", "text", "file"], "output": ["text"]}, "provider": "openai", "profile_image_url": "/static/openai.png"}}, "context_length": 1047576, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "gpt-4.1-nano", "object": "model", "created": **********, "owned_by": "openai", "name": "OpenAI: GPT-4.1 Nano", "openai": {"id": "gpt-4.1-nano", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "For tasks that demand low latency, GPT‑4.1 nano is the fastest and cheapest model in the GPT-4.1 series. It delivers exceptional performance at a small size with its 1 million token context window, and scores 80.1% on MMLU, 50.3% on GPQA, and 9.8% on Aider polyglot coding – even higher than GPT‑4o mini. It’s ideal for tasks like classification or autocompletion.", "modalities": {"input": ["image", "text", "file"], "output": ["text"]}, "provider": "openai", "profile_image_url": "/static/openai.png"}}, "context_length": 1047576, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "gpt-4.5-preview", "object": "model", "created": **********, "owned_by": "openai", "name": "OpenAI: GPT-4.5 (Preview)", "openai": {"id": "gpt-4.5-preview", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "GPT-4.5 (Preview) is a research preview of OpenAI’s latest language model, designed to advance capabilities in reasoning, creativity, and multi-turn conversation. It builds on previous iterations with improvements in world knowledge, contextual coherence, and the ability to follow user intent more effectively.\n\nThe model demonstrates enhanced performance in tasks that require open-ended thinking, problem-solving, and communication. Early testing suggests it is better at generating nuanced responses, maintaining long-context coherence, and reducing hallucinations compared to earlier versions.\n\nThis research preview is intended to help evaluate GPT-4.5’s strengths and limitations in real-world use cases as OpenAI continues to refine and develop future models. Read more at the [blog post here.](https://openai.com/index/introducing-gpt-4-5/)", "modalities": {"input": ["text", "image"], "output": ["text"]}, "provider": "openai", "profile_image_url": "/static/openai.png"}}, "context_length": 128000, "reasoning_level": "light", "premium_model": true, "actions": [], "tags": []}, {"id": "gpt-4o:extended", "object": "model", "created": **********, "owned_by": "openai", "name": "OpenAI: GPT-4o (extended)", "openai": {"id": "gpt-4o:extended", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "GPT-4o (\"o\" for \"omni\") is OpenAI's latest AI model, supporting both text and image inputs with text outputs. It maintains the intelligence level of [GPT-4 Turbo](/models/openai/gpt-4-turbo) while being twice as fast and 50% more cost-effective. GPT-4o also offers improved performance in processing non-English languages and enhanced visual capabilities.\n\nFor benchmarking against other models, it was briefly called [\"im-also-a-good-gpt2-chatbot\"](https://twitter.com/LiamFedus/status/1790064963966370209)\n\n#multimodal", "modalities": {"input": ["text", "image", "file"], "output": ["text"]}, "provider": "openai", "profile_image_url": "/static/openai.png"}}, "context_length": 128000, "reasoning_level": "light", "premium_model": false, "actions": [], "tags": []}, {"id": "o1", "object": "model", "created": **********, "owned_by": "openai", "name": "OpenAI: o1", "openai": {"id": "o1", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "The latest and strongest model family from OpenAI, o1 is designed to spend more time thinking before responding. The o1 model series is trained with large-scale reinforcement learning to reason using chain of thought. \n\nThe o1 models are optimized for math, science, programming, and other STEM-related tasks. They consistently exhibit PhD-level accuracy on benchmarks in physics, chemistry, and biology. Learn more in the [launch announcement](https://openai.com/o1).\n", "modalities": {"input": ["text", "image"], "output": ["text"]}, "provider": "openai", "profile_image_url": "/static/openai.png"}}, "context_length": 200000, "reasoning_level": "high", "auto_select_priority": 0, "premium_model": true, "actions": [], "tags": []}, {"id": "o1-mini", "object": "model", "created": **********, "owned_by": "openai", "name": "OpenAI: o1-mini", "openai": {"id": "o1-mini", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "The latest and strongest model family from OpenAI, o1 is designed to spend more time thinking before responding.\n\nThe o1 models are optimized for math, science, programming, and other STEM-related tasks. They consistently exhibit PhD-level accuracy on benchmarks in physics, chemistry, and biology. Learn more in the [launch announcement](https://openai.com/o1).\n\nNote: This model is currently experimental and not suitable for production use-cases, and may be heavily rate-limited.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "openai", "profile_image_url": "/static/openai.png"}}, "context_length": 128000, "reasoning_level": "light", "premium_model": false, "actions": [], "tags": []}, {"id": "o3", "object": "model", "created": **********, "owned_by": "openai", "name": "OpenAI: o3", "openai": {"id": "o3", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "o3 is a well-rounded and powerful model across domains. It sets a new standard for math, science, coding, and visual reasoning tasks. It also excels at technical writing and instruction-following. Use it to think through multi-step problems that involve analysis across text, code, and images. Note that BYOK is required for this model. Set up here: https://openrouter.ai/settings/integrations", "modalities": {"input": ["image", "text", "file"], "output": ["text"]}, "provider": "openai", "profile_image_url": "/static/openai.png"}}, "context_length": 200000, "reasoning_level": "heavy", "premium_model": false, "actions": [], "tags": []}, {"id": "o3-mini", "object": "model", "created": **********, "owned_by": "openai", "name": "OpenAI: o3 Mini", "openai": {"id": "o3-mini", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "OpenAI o3-mini is a cost-efficient language model optimized for STEM reasoning tasks, particularly excelling in science, mathematics, and coding.\n\nThe model demonstrates significant improvements over its predecessor, with expert testers preferring its responses 56% of the time and noting a 39% reduction in major errors on complex questions. With medium reasoning effort settings, o3-mini matches the performance of the larger o1 model on challenging reasoning evaluations like AIME and GPQA, while maintaining lower latency and cost.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "openai", "profile_image_url": "/static/openai.png"}}, "context_length": 200000, "reasoning_level": "light", "auto_select_priority": 0, "premium_model": false, "actions": [], "tags": []}, {"id": "o3-mini-high", "object": "model", "created": **********, "owned_by": "openai", "name": "OpenAI: o3 Mini High", "openai": {"id": "o3-mini-high", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "OpenAI o3-mini-high is the same model as [o3-mini](/openai/o3-mini) with reasoning_effort set to high. \n\no3-mini is a cost-efficient language model optimized for STEM reasoning tasks, particularly excelling in science, mathematics, and coding. The model features three adjustable reasoning effort levels and supports key developer capabilities including function calling, structured outputs, and streaming, though it does not include vision processing capabilities.\n\nThe model demonstrates significant improvements over its predecessor, with expert testers preferring its responses 56% of the time and noting a 39% reduction in major errors on complex questions. With medium reasoning effort settings, o3-mini matches the performance of the larger o1 model on challenging reasoning evaluations like AIME and GPQA, while maintaining lower latency and cost.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "openai", "profile_image_url": "/static/openai.png"}}, "context_length": 200000, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "o4-mini", "object": "model", "created": **********, "owned_by": "openai", "name": "OpenAI: o4 Mini", "openai": {"id": "o4-mini", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "OpenAI o4-mini is a compact reasoning model in the o-series, optimized for fast, cost-efficient performance while retaining strong multimodal and agentic capabilities. It supports tool use and demonstrates competitive reasoning and coding performance across benchmarks like AIME (99.5% with Python) and SWE-bench, outperforming its predecessor o3-mini and even approaching o3 in some domains.\n\nDespite its smaller size, o4-mini exhibits high accuracy in STEM tasks, visual problem solving (e.g., MathVista, MMMU), and code editing. It is especially well-suited for high-throughput scenarios where latency or cost is critical. Thanks to its efficient architecture and refined reinforcement learning training, o4-mini can chain tools, generate structured outputs, and solve multi-step tasks with minimal delay—often in under a minute.", "modalities": {"input": ["image", "text"], "output": ["text"]}, "provider": "openai", "profile_image_url": "/static/openai.png"}}, "context_length": 200000, "reasoning_level": "light", "premium_model": false, "actions": [], "tags": []}, {"id": "o4-mini-high", "object": "model", "created": **********, "owned_by": "openai", "name": "OpenAI: o4 Mini High", "openai": {"id": "o4-mini-high", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "OpenAI o4-mini-high is the same model as [o4-mini](/openai/o4-mini) with reasoning_effort set to high. \n\nOpenAI o4-mini is a compact reasoning model in the o-series, optimized for fast, cost-efficient performance while retaining strong multimodal and agentic capabilities. It supports tool use and demonstrates competitive reasoning and coding performance across benchmarks like AIME (99.5% with Python) and SWE-bench, outperforming its predecessor o3-mini and even approaching o3 in some domains.\n\nDespite its smaller size, o4-mini exhibits high accuracy in STEM tasks, visual problem solving (e.g., MathVista, MMMU), and code editing. It is especially well-suited for high-throughput scenarios where latency or cost is critical. Thanks to its efficient architecture and refined reinforcement learning training, o4-mini can chain tools, generate structured outputs, and solve multi-step tasks with minimal delay—often in under a minute.", "modalities": {"input": ["image", "text", "file"], "output": ["text"]}, "provider": "openai", "profile_image_url": "/static/openai.png"}}, "context_length": 200000, "reasoning_level": "heavy", "premium_model": false, "actions": [], "tags": []}, {"id": "r1-1776", "object": "model", "created": **********, "owned_by": "perplexity", "name": "Perplexity: R1 1776", "openai": {"id": "r1-1776", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "R1 1776 is a version of DeepSeek-R1 that has been post-trained to remove censorship constraints related to topics restricted by the Chinese government. The model retains its original reasoning capabilities while providing direct responses to a wider range of queries. R1 1776 is an offline chat model that does not use the perplexity search subsystem.\n\nThe model was tested on a multilingual dataset of over 1,000 examples covering sensitive topics to measure its likelihood of refusal or overly filtered responses. [Evaluation Results](https://cdn-uploads.huggingface.co/production/uploads/675c8332d01f593dc90817f5/GiN2VqC5hawUgAGJ6oHla.png) Its performance on math and reasoning benchmarks remains similar to the base R1 model. [Reasoning Performance](https://cdn-uploads.huggingface.co/production/uploads/675c8332d01f593dc90817f5/n4Z9Byqp2S7sKUvCvI40R.png)\n\nRead more on the [Blog Post](https://perplexity.ai/hub/blog/open-sourcing-r1-1776)", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "perplexity", "profile_image_url": "/static/perplexity.png"}}, "context_length": 128000, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "sonar", "object": "model", "created": **********, "owned_by": "perplexity", "name": "Perplexity: Sonar", "openai": {"id": "sonar", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Sonar is lightweight, affordable, fast, and simple to use — now featuring citations and the ability to customize sources. It is designed for companies seeking to integrate lightweight question-and-answer features optimized for speed.", "modalities": {"input": ["text", "image"], "output": ["text"]}, "provider": "perplexity", "profile_image_url": "/static/perplexity.png"}}, "context_length": 127072, "reasoning_level": "fast", "auto_select_priority": 0, "premium_model": false, "actions": [], "tags": []}, {"id": "sonar-reasoning", "object": "model", "created": **********, "owned_by": "perplexity", "name": "Perplexity: <PERSON><PERSON> Reasoning", "openai": {"id": "sonar-reasoning", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Sonar Reasoning is a reasoning model provided by Perplexity based on [DeepSeek R1](/deepseek/deepseek-r1).\n\nIt allows developers to utilize long chain of thought with built-in web search. Sonar Reasoning is uncensored and hosted in US datacenters. ", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "perplexity", "profile_image_url": "/static/perplexity.png"}}, "context_length": 127000, "reasoning_level": "light", "auto_select_priority": 0, "premium_model": false, "actions": [], "tags": []}, {"id": "sonar-reasoning-pro", "object": "model", "created": **********, "owned_by": "perplexity", "name": "Perplexity: Sonar Reasoning Pro", "openai": {"id": "sonar-reasoning-pro", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Note: Sonar Pro pricing includes Perplexity search pricing. See [details here](https://docs.perplexity.ai/guides/pricing#detailed-pricing-breakdown-for-sonar-reasoning-pro-and-sonar-pro)\n\nSonar Reasoning Pro is a premier reasoning model powered by DeepSeek R1 with Chain of Thought (CoT). Designed for advanced use cases, it supports in-depth, multi-step queries with a larger context window and can surface more citations per search, enabling more comprehensive and extensible responses.", "modalities": {"input": ["text", "image"], "output": ["text"]}, "provider": "perplexity", "profile_image_url": "/static/perplexity.png"}}, "context_length": 128000, "reasoning_level": "heavy", "auto_select_priority": 0, "premium_model": false, "actions": [], "tags": []}, {"id": "qwen-2-72b-instruct", "object": "model", "created": **********, "owned_by": "qwen", "name": "Qwen 2 72B Instruct", "openai": {"id": "qwen-2-72b-instruct", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Qwen2 72B is a transformer-based model that excels in language understanding, multilingual capabilities, coding, mathematics, and reasoning.\n\nIt features SwiGLU activation, attention QKV bias, and group query attention. It is pretrained on extensive data with supervised finetuning and direct preference optimization.\n\nFor more details, see this [blog post](https://qwenlm.github.io/blog/qwen2/) and [GitHub repo](https://github.com/QwenLM/Qwen2).\n\nUsage of this model is subject to [Tongyi Qianwen LICENSE AGREEMENT](https://huggingface.co/Qwen/Qwen1.5-110B-Chat/blob/main/LICENSE).", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "qwen", "profile_image_url": "/static/qwen.png"}}, "context_length": 32768, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "qwen-2.5-72b-instruct", "object": "model", "created": **********, "owned_by": "qwen", "name": "Qwen2.5 72B Instruct", "openai": {"id": "qwen-2.5-72b-instruct", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Qwen2.5 72B is the latest series of Qwen large language models. Qwen2.5 brings the following improvements upon Qwen2:\n\n- Significantly more knowledge and has greatly improved capabilities in coding and mathematics, thanks to our specialized expert models in these domains.\n\n- Significant improvements in instruction following, generating long texts (over 8K tokens), understanding structured data (e.g, tables), and generating structured outputs especially JSON. More resilient to the diversity of system prompts, enhancing role-play implementation and condition-setting for chatbots.\n\n- Long-context Support up to 128K tokens and can generate up to 8K tokens.\n\n- Multilingual support for over 29 languages, including Chinese, English, French, Spanish, Portuguese, German, Italian, Russian, Japanese, Korean, Vietnamese, Thai, Arabic, and more.\n\nUsage of this model is subject to [Tongyi Qianwen LICENSE AGREEMENT](https://huggingface.co/Qwen/Qwen1.5-110B-Chat/blob/main/LICENSE).", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "qwen", "profile_image_url": "/static/qwen.png"}}, "context_length": 131072, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "qwen-2.5-7b-instruct", "object": "model", "created": **********, "owned_by": "qwen", "name": "Qwen2.5 7B Instruct", "openai": {"id": "qwen-2.5-7b-instruct", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Qwen2.5 7B is the latest series of Qwen large language models. Qwen2.5 brings the following improvements upon Qwen2:\n\n- Significantly more knowledge and has greatly improved capabilities in coding and mathematics, thanks to our specialized expert models in these domains.\n\n- Significant improvements in instruction following, generating long texts (over 8K tokens), understanding structured data (e.g, tables), and generating structured outputs especially JSON. More resilient to the diversity of system prompts, enhancing role-play implementation and condition-setting for chatbots.\n\n- Long-context Support up to 128K tokens and can generate up to 8K tokens.\n\n- Multilingual support for over 29 languages, including Chinese, English, French, Spanish, Portuguese, German, Italian, Russian, Japanese, Korean, Vietnamese, Thai, Arabic, and more.\n\nUsage of this model is subject to [Tongyi Qianwen LICENSE AGREEMENT](https://huggingface.co/Qwen/Qwen1.5-110B-Chat/blob/main/LICENSE).", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "qwen", "profile_image_url": "/static/qwen.png"}}, "context_length": 32768, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "qwen-2.5-coder-32b-instruct", "object": "model", "created": **********, "owned_by": "qwen", "name": "Qwen2.5 Coder 32B Instruct", "openai": {"id": "qwen-2.5-coder-32b-instruct", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Qwen2.5-Coder is the latest series of Code-Specific Qwen large language models (formerly known as CodeQwen). Qwen2.5-Coder brings the following improvements upon CodeQwen1.5:\n\n- Significantly improvements in **code generation**, **code reasoning** and **code fixing**. \n- A more comprehensive foundation for real-world applications such as **Code Agents**. Not only enhancing coding capabilities but also maintaining its strengths in mathematics and general competencies.\n\nTo read more about its evaluation results, check out [<PERSON>wen 2.5 Coder's blog](https://qwenlm.github.io/blog/qwen2.5-coder-family/).", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "qwen", "profile_image_url": "/static/qwen.png"}}, "context_length": 33000, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "qwq-32b", "object": "model", "created": **********, "owned_by": "qwen", "name": "Qwen: QwQ 32B", "openai": {"id": "qwq-32b", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "QwQ is the reasoning model of the Qwen series. Compared with conventional instruction-tuned models, QwQ, which is capable of thinking and reasoning, can achieve significantly enhanced performance in downstream tasks, especially hard problems. QwQ-32B is the medium-sized reasoning model, which is capable of achieving competitive performance against state-of-the-art reasoning models, e.g., DeepSeek-R1, o1-mini.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "qwen", "profile_image_url": "/static/qwen.png"}}, "context_length": 131072, "reasoning_level": "heavy", "premium_model": false, "actions": [], "tags": []}, {"id": "qwq-32b-preview", "object": "model", "created": **********, "owned_by": "qwen", "name": "Qwen: QwQ 32B Preview", "openai": {"id": "qwq-32b-preview", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "QwQ-32B-Preview is an experimental research model focused on AI reasoning capabilities developed by the Qwen Team. As a preview release, it demonstrates promising analytical abilities while having several important limitations:\n\n1. **Language Mixing and Code-Switching**: The model may mix languages or switch between them unexpectedly, affecting response clarity.\n2. **Recursive Reasoning Loops**: The model may enter circular reasoning patterns, leading to lengthy responses without a conclusive answer.\n3. **Safety and Ethical Considerations**: The model requires enhanced safety measures to ensure reliable and secure performance, and users should exercise caution when deploying it.\n4. **Performance and Benchmark Limitations**: The model excels in math and coding but has room for improvement in other areas, such as common sense reasoning and nuanced language understanding.\n\n", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "qwen", "profile_image_url": "/static/qwen.png"}}, "context_length": 32768, "reasoning_level": "heavy", "premium_model": false, "actions": [], "tags": []}, {"id": "qwen2.5-vl-32b-instruct", "object": "model", "created": **********, "owned_by": "qwen", "name": "Qwen: Qwen2.5 VL 32B Instruct", "openai": {"id": "qwen2.5-vl-32b-instruct", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Qwen2.5-VL-32B is a multimodal vision-language model fine-tuned through reinforcement learning for enhanced mathematical reasoning, structured outputs, and visual problem-solving capabilities. It excels at visual analysis tasks, including object recognition, textual interpretation within images, and precise event localization in extended videos. Qwen2.5-VL-32B demonstrates state-of-the-art performance across multimodal benchmarks such as MMMU, MathVista, and VideoMME, while maintaining strong reasoning and clarity in text-based tasks like MMLU, mathematical problem-solving, and code generation.", "modalities": {"input": ["text", "image"], "output": ["text"]}, "provider": "qwen", "profile_image_url": "/static/qwen.png"}}, "context_length": 128000, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "qwen2.5-vl-72b-instruct", "object": "model", "created": **********, "owned_by": "qwen", "name": "Qwen: Qwen2.5 VL 72B Instruct", "openai": {"id": "qwen2.5-vl-72b-instruct", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Qwen2.5-VL is proficient in recognizing common objects such as flowers, birds, fish, and insects. It is also highly capable of analyzing texts, charts, icons, graphics, and layouts within images.", "modalities": {"input": ["text", "image"], "output": ["text"]}, "provider": "qwen", "profile_image_url": "/static/qwen.png"}}, "context_length": 128000, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "qwen-2.5-vl-7b-instruct", "object": "model", "created": **********, "owned_by": "qwen", "name": "Qwen: Qwen2.5-VL 7B Instruct", "openai": {"id": "qwen-2.5-vl-7b-instruct", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Qwen2.5 VL 7B is a multimodal LLM from the Qwen Team with the following key enhancements:\n\n- SoTA understanding of images of various resolution & ratio: Qwen2.5-VL achieves state-of-the-art performance on visual understanding benchmarks, including MathVista, DocVQA, RealWorldQA, MTVQA, etc.\n\n- Understanding videos of 20min+: Qwen2.5-<PERSON><PERSON> can understand videos over 20 minutes for high-quality video-based question answering, dialog, content creation, etc.\n\n- Agent that can operate your mobiles, robots, etc.: with the abilities of complex reasoning and decision making, Qwen2.5-VL can be integrated with devices like mobile phones, robots, etc., for automatic operation based on visual environment and text instructions.\n\n- Multilingual Support: to serve global users, besides English and Chinese, Qwen2.5-VL now supports the understanding of texts in different languages inside images, including most European languages, Japanese, Korean, Arabic, Vietnamese, etc.\n\nFor more details, see this [blog post](https://qwenlm.github.io/blog/qwen2-vl/) and [GitHub repo](https://github.com/QwenLM/Qwen2-VL).\n\nUsage of this model is subject to [Tongyi Qianwen LICENSE AGREEMENT](https://huggingface.co/Qwen/Qwen1.5-110B-Chat/blob/main/LICENSE).", "modalities": {"input": ["text", "image"], "output": ["text"]}, "provider": "qwen", "profile_image_url": "/static/qwen.png"}}, "context_length": 32768, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "qwen3-14b", "object": "model", "created": **********, "owned_by": "qwen", "name": "Qwen: <PERSON>wen3 14B", "openai": {"id": "qwen3-14b", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Qwen3-14B is a dense 14.8B parameter causal language model from the Qwen3 series, designed for both complex reasoning and efficient dialogue. It supports seamless switching between a \"thinking\" mode for tasks like math, programming, and logical inference, and a \"non-thinking\" mode for general-purpose conversation. The model is fine-tuned for instruction-following, agent tool use, creative writing, and multilingual tasks across 100+ languages and dialects. It natively handles 32K token contexts and can extend to 131K tokens using YaRN-based scaling.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "qwen", "profile_image_url": "/static/qwen.png"}}, "context_length": 40960, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "qwen3-235b-a22b", "object": "model", "created": **********, "owned_by": "qwen", "name": "Qwen: Qwen3 235B A22B", "openai": {"id": "qwen3-235b-a22b", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Qwen3-235B-A22B is a 235B parameter mixture-of-experts (MoE) model developed by Qwen, activating 22B parameters per forward pass. It supports seamless switching between a \"thinking\" mode for complex reasoning, math, and code tasks, and a \"non-thinking\" mode for general conversational efficiency. The model demonstrates strong reasoning ability, multilingual support (100+ languages and dialects), advanced instruction-following, and agent tool-calling capabilities. It natively handles a 32K token context window and extends up to 131K tokens using YaRN-based scaling.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "qwen", "profile_image_url": "/static/qwen.png"}}, "context_length": 32768, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "qwen3-30b-a3b", "object": "model", "created": **********, "owned_by": "qwen", "name": "Qwen: Qwen3 30B A3B", "openai": {"id": "qwen3-30b-a3b", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Qwen3, the latest generation in the Qwen large language model series, features both dense and mixture-of-experts (MoE) architectures to excel in reasoning, multilingual support, and advanced agent tasks. Its unique ability to switch seamlessly between a thinking mode for complex reasoning and a non-thinking mode for efficient dialogue ensures versatile, high-quality performance.\n\nSignificantly outperforming prior models like QwQ and Qwen2.5, Qwen3 delivers superior mathematics, coding, commonsense reasoning, creative writing, and interactive dialogue capabilities. The Qwen3-30B-A3B variant includes 30.5 billion parameters (3.3 billion activated), 48 layers, 128 experts (8 activated per task), and supports up to 131K token contexts with YaRN, setting a new standard among open-source models.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "qwen", "profile_image_url": "/static/qwen.png"}}, "context_length": 40960, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "qwen3-32b", "object": "model", "created": **********, "owned_by": "qwen", "name": "Qwen: <PERSON>wen3 32B", "openai": {"id": "qwen3-32b", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Qwen3-32B is a dense 32.8B parameter causal language model from the Qwen3 series, optimized for both complex reasoning and efficient dialogue. It supports seamless switching between a \"thinking\" mode for tasks like math, coding, and logical inference, and a \"non-thinking\" mode for faster, general-purpose conversation. The model demonstrates strong performance in instruction-following, agent tool use, creative writing, and multilingual tasks across 100+ languages and dialects. It natively handles 32K token contexts and can extend to 131K tokens using YaRN-based scaling. ", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "qwen", "profile_image_url": "/static/qwen.png"}}, "context_length": 40960, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "qwen3-8b", "object": "model", "created": **********, "owned_by": "qwen", "name": "Qwen: <PERSON>wen3 8B", "openai": {"id": "qwen3-8b", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Qwen3-8B is a dense 8.2B parameter causal language model from the Qwen3 series, designed for both reasoning-heavy tasks and efficient dialogue. It supports seamless switching between \"thinking\" mode for math, coding, and logical inference, and \"non-thinking\" mode for general conversation. The model is fine-tuned for instruction-following, agent integration, creative writing, and multilingual use across 100+ languages and dialects. It natively supports a 32K token context window and can extend to 131K tokens with YaRN scaling.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "qwen", "profile_image_url": "/static/qwen.png"}}, "context_length": 128000, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "glm-4-32b", "object": "model", "created": **********, "owned_by": "thudm", "name": "THUDM: GLM 4 32B", "openai": {"id": "glm-4-32b", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "GLM-4-32B-0414 is a 32B bilingual (Chinese-English) open-weight language model optimized for code generation, function calling, and agent-style tasks. Pretrained on 15T of high-quality and reasoning-heavy data, it was further refined using human preference alignment, rejection sampling, and reinforcement learning. The model excels in complex reasoning, artifact generation, and structured output tasks, achieving performance comparable to GPT-4o and DeepSeek-V3-0324 across several benchmarks.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "thudm", "profile_image_url": "/static/favicon.png"}}, "context_length": 32000, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "glm-z1-32b", "object": "model", "created": **********, "owned_by": "thudm", "name": "THUDM: GLM Z1 32B", "openai": {"id": "glm-z1-32b", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "GLM-Z1-32B-0414 is an enhanced reasoning variant of GLM-4-32B, built for deep mathematical, logical, and code-oriented problem solving. It applies extended reinforcement learning—both task-specific and general pairwise preference-based—to improve performance on complex multi-step tasks. Compared to the base GLM-4-32B model, Z1 significantly boosts capabilities in structured reasoning and formal domains.\n\nThe model supports enforced “thinking” steps via prompt engineering and offers improved coherence for long-form outputs. It’s optimized for use in agentic workflows, and includes support for long context (via YaRN), JSON tool calling, and fine-grained sampling configuration for stable inference. Ideal for use cases requiring deliberate, multi-step reasoning or formal derivations.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "thudm", "profile_image_url": "/static/favicon.png"}}, "context_length": 32000, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "glm-z1-rumination-32b", "object": "model", "created": **********, "owned_by": "thudm", "name": "THUDM: GLM Z1 Rumination 32B ", "openai": {"id": "glm-z1-rumination-32b", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "THUDM: GLM Z1 Rumination 32B is a 32B-parameter deep reasoning model from the GLM-4-Z1 series, optimized for complex, open-ended tasks requiring prolonged deliberation. It builds upon glm-4-32b-0414 with additional reinforcement learning phases and multi-stage alignment strategies, introducing “rumination” capabilities designed to emulate extended cognitive processing. This includes iterative reasoning, multi-hop analysis, and tool-augmented workflows such as search, retrieval, and citation-aware synthesis.\n\nThe model excels in research-style writing, comparative analysis, and intricate question answering. It supports function calling for search and navigation primitives (`search`, `click`, `open`, `finish`), enabling use in agent-style pipelines. Rumination behavior is governed by multi-turn loops with rule-based reward shaping and delayed decision mechanisms, benchmarked against Deep Research frameworks such as OpenAI’s internal alignment stacks. This variant is suitable for scenarios requiring depth over speed.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "thudm", "profile_image_url": "/static/favicon.png"}}, "context_length": 32000, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "uns-alpha1.uns_alpha1", "name": "Uns Alpha1", "object": "model", "created": **********, "owned_by": "openai", "pipe": {"type": "pipe"}, "info": {"meta": {}}, "actions": [], "tags": []}, {"id": "uns-alpha-mini.uns_alpha_mini", "name": "Uns Alpha1 Mini", "object": "model", "created": **********, "owned_by": "openai", "pipe": {"type": "pipe"}, "info": {"meta": {}}, "actions": [], "tags": []}, {"id": "uns-alpha-nano.uns_alpha_nano", "name": "Uns Alpha1 Nano", "object": "model", "created": **********, "owned_by": "openai", "pipe": {"type": "pipe"}, "info": {"meta": {}}, "actions": [], "tags": []}, {"id": "wizardlm-2-8x22b", "object": "model", "created": **********, "owned_by": "microsoft", "name": "WizardLM-2 8x22B", "openai": {"id": "wizardlm-2-8x22b", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "WizardLM-2 8x22B is Microsoft AI's most advanced Wizard model. It demonstrates highly competitive performance compared to leading proprietary models, and it consistently outperforms all existing state-of-the-art opensource models.\n\nIt is an instruct finetune of [Mixtral 8x22B](/models/mistralai/mixtral-8x22b).\n\nTo read more about the model release, [click here](https://wizardlm.github.io/WizardLM2/).\n\n#moe", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "microsoft", "profile_image_url": "/static/microsoft.png"}}, "context_length": 65536, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "allam-2-7b", "object": "model", "created": **********, "owned_by": "groq", "name": "allam-2-7b", "openai": {"id": "allam-2-7b", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"modalities": {"input": ["text"], "output": ["text"]}, "provider": "groq", "profile_image_url": "/static/favicon.png"}}, "context_length": 4096, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "compound-beta", "object": "model", "created": **********, "owned_by": "groq", "name": "compound-beta", "openai": {"id": "compound-beta", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"modalities": {"input": ["text"], "output": ["text"]}, "provider": "groq", "profile_image_url": "/static/favicon.png"}}, "context_length": 8192, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "compound-beta-mini", "object": "model", "created": **********, "owned_by": "groq", "name": "compound-beta-mini", "openai": {"id": "compound-beta-mini", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"modalities": {"input": ["text"], "output": ["text"]}, "provider": "groq", "profile_image_url": "/static/favicon.png"}}, "context_length": 8192, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "deepseek-r1-distill-llama-70b", "object": "model", "created": **********, "owned_by": "groq", "name": "deepseek-r1-distill-llama-70b", "openai": {"id": "deepseek-r1-distill-llama-70b", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"modalities": {"input": ["text"], "output": ["text"]}, "provider": "groq", "profile_image_url": "/static/favicon.png"}}, "context_length": 131072, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "gemini-2.0-flash", "object": "model", "created": **********, "owned_by": "google", "name": "gemini-2.0-flash", "openai": {"id": "gemini-2.0-flash", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"modalities": {"input": ["text"], "output": ["text"]}, "provider": "google", "profile_image_url": "/static/google.png"}}, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "gemma-3-1b-it", "object": "model", "created": **********, "owned_by": "google", "name": "gemma-3-1b-it", "openai": {"id": "gemma-3-1b-it", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"modalities": {"input": ["text"], "output": ["text"]}, "provider": "google", "profile_image_url": "/static/google.png"}}, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "llama-3.3-70b-versatile", "object": "model", "created": **********, "owned_by": "groq", "name": "llama-3.3-70b-versatile", "openai": {"id": "llama-3.3-70b-versatile", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"modalities": {"input": ["text"], "output": ["text"]}, "provider": "groq", "profile_image_url": "/static/favicon.png"}}, "context_length": 131072, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "qwen-qwq-32b", "object": "model", "created": **********, "owned_by": "groq", "name": "qwen-qwq-32b", "openai": {"id": "qwen-qwq-32b", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"modalities": {"input": ["text"], "output": ["text"]}, "provider": "groq", "profile_image_url": "/static/favicon.png"}}, "context_length": 131072, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "grok-2-1212", "object": "model", "created": **********, "owned_by": "x-ai", "name": "xAI: Grok 2 1212", "openai": {"id": "grok-2-1212", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Grok 2 1212 introduces significant enhancements to accuracy, instruction adherence, and multilingual support, making it a powerful and flexible choice for developers seeking a highly steerable, intelligent model.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "x-ai", "profile_image_url": "/static/favicon.png"}}, "context_length": 131072, "reasoning_level": "light", "premium_model": false, "actions": [], "tags": []}, {"id": "grok-3-beta", "object": "model", "created": **********, "owned_by": "x-ai", "name": "xAI: Grok 3 Beta", "openai": {"id": "grok-3-beta", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Grok 3 is the latest model from xAI. It's their flagship model that excels at enterprise use cases like data extraction, coding, and text summarization. Possesses deep domain knowledge in finance, healthcare, law, and science.\n\nExcels in structured tasks and benchmarks like GPQA, LCB, and MMLU-Pro where it outperforms Grok 3 Mini even on high thinking. \n\nNote: That there are two xAI endpoints for this model. By default when using this model we will always route you to the base endpoint. If you want the fast endpoint you can add `provider: { sort: throughput}`, to sort by throughput instead. \n", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "x-ai", "profile_image_url": "/static/favicon.png"}}, "context_length": 131072, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "grok-3-mini-beta", "object": "model", "created": **********, "owned_by": "x-ai", "name": "xAI: Grok 3 Mini Beta", "openai": {"id": "grok-3-mini-beta", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Grok 3 Mini is a lightweight, smaller thinking model. Unlike traditional models that generate answers immediately, Grok 3 Mini thinks before responding. It’s ideal for reasoning-heavy tasks that don’t demand extensive domain knowledge, and shines in math-specific and quantitative use cases, such as solving challenging puzzles or math problems.\n\nTransparent \"thinking\" traces accessible. Defaults to low reasoning, can boost with setting `reasoning: { effort: \"high\" }`\n\nNote: That there are two xAI endpoints for this model. By default when using this model we will always route you to the base endpoint. If you want the fast endpoint you can add `provider: { sort: throughput}`, to sort by throughput instead. \n", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "x-ai", "profile_image_url": "/static/favicon.png"}}, "context_length": 131072, "reasoning_level": "fast", "premium_model": false, "actions": [], "tags": []}, {"id": "grok-beta", "object": "model", "created": **********, "owned_by": "x-ai", "name": "xAI: Grok Beta", "openai": {"id": "grok-beta", "object": "model", "created": **********, "owned_by": "openai"}, "urlIdx": 0, "info": {"meta": {"description": "Grok Beta is xAI's experimental language model with state-of-the-art reasoning capabilities, best for complex and multi-step use cases.\n\nIt is the successor of [Grok 2](https://x.ai/blog/grok-2) with enhanced context length.", "modalities": {"input": ["text"], "output": ["text"]}, "provider": "x-ai", "profile_image_url": "/static/favicon.png"}}, "context_length": 131072, "reasoning_level": "light", "premium_model": false, "actions": [], "tags": []}]