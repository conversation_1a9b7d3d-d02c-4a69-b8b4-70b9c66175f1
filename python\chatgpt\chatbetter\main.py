import json
import os
import time
import uuid
import threading
from typing import Any, Dict, List, Optional, TypedDict, Union

import requests
from fastapi import FastAP<PERSON>, HTTPException, Depends, Query
from fastapi.responses import StreamingResponse
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel, Field


# ChatBetter Account Management
class ChatBetterAccount(TypedDict):
    auth_token: str
    accessToken: str
    is_valid: bool
    last_used: float
    error_count: int


# Global variables
VALID_CLIENT_KEYS: set = set()
CHATBETTER_ACCOUNTS: List[ChatBetterAccount] = []
CHATBETTER_MODELS: List[Dict[str, Any]] = []
account_rotation_lock = threading.Lock()
MAX_ERROR_COUNT = 3
ERROR_COOLDOWN = 300  # 5 minutes cooldown for accounts with errors
DEBUG_MODE = os.environ.get("DEBUG_MODE", "false").lower() == "true"


# Pydantic Models
class ChatMessage(BaseModel):
    role: str
    content: Union[str, List[Dict[str, Any]]]
    reasoning_content: Optional[str] = None


class ChatCompletionRequest(BaseModel):
    model: str
    messages: List[ChatMessage]
    stream: bool = True
    temperature: Optional[float] = None
    max_tokens: Optional[int] = None
    top_p: Optional[float] = None
    raw_response: bool = False  # 新增参数：是否返回原始响应


class ModelInfo(BaseModel):
    id: str
    object: str = "model"
    created: int
    owned_by: str


class ModelList(BaseModel):
    object: str = "list"
    data: List[ModelInfo]


class ChatCompletionChoice(BaseModel):
    message: ChatMessage
    index: int = 0
    finish_reason: str = "stop"


class ChatCompletionResponse(BaseModel):
    id: str = Field(default_factory=lambda: f"chatcmpl-{uuid.uuid4().hex}")
    object: str = "chat.completion"
    created: int = Field(default_factory=lambda: int(time.time()))
    model: str
    choices: List[ChatCompletionChoice]
    usage: Dict[str, int] = Field(
        default_factory=lambda: {
            "prompt_tokens": 0,
            "completion_tokens": 0,
            "total_tokens": 0,
        }
    )


class StreamChoice(BaseModel):
    delta: Dict[str, Any] = Field(default_factory=dict)
    index: int = 0
    finish_reason: Optional[str] = None


class StreamResponse(BaseModel):
    id: str = Field(default_factory=lambda: f"chatcmpl-{uuid.uuid4().hex}")
    object: str = "chat.completion.chunk"
    created: int = Field(default_factory=lambda: int(time.time()))
    model: str
    choices: List[StreamChoice]


# FastAPI App
app = FastAPI(title="ChatBetter OpenAI API Adapter")
security = HTTPBearer(auto_error=False)


def log_debug(message: str):
    """Debug日志函数"""
    if DEBUG_MODE:
        print(f"[DEBUG] {message}")


def load_client_api_keys():
    """Load client API keys from client_api_keys.json"""
    global VALID_CLIENT_KEYS
    try:
        with open("client_api_keys.json", "r", encoding="utf-8") as f:
            keys = json.load(f)
            VALID_CLIENT_KEYS = set(keys) if isinstance(keys, list) else set()
            print(f"Successfully loaded {len(VALID_CLIENT_KEYS)} client API keys.")
    except FileNotFoundError:
        print("Error: client_api_keys.json not found. Client authentication will fail.")
        VALID_CLIENT_KEYS = set()
    except Exception as e:
        print(f"Error loading client_api_keys.json: {e}")
        VALID_CLIENT_KEYS = set()


def load_chatbetter_accounts():
    """Load ChatBetter accounts from chatbetter.json"""
    global CHATBETTER_ACCOUNTS
    CHATBETTER_ACCOUNTS = []
    try:
        with open("chatbetter.json", "r", encoding="utf-8") as f:
            accounts = json.load(f)
            if not isinstance(accounts, list):
                print("Warning: chatbetter.json should contain a list of account objects.")
                return

            for acc in accounts:
                auth_token = acc.get("auth_token")
                access_token = acc.get("accessToken")
                if auth_token and access_token:
                    CHATBETTER_ACCOUNTS.append({
                        "auth_token": auth_token,
                        "accessToken": access_token,
                        "is_valid": True,
                        "last_used": 0,
                        "error_count": 0
                    })
            print(f"Successfully loaded {len(CHATBETTER_ACCOUNTS)} ChatBetter accounts.")
    except FileNotFoundError:
        print("Error: chatbetter.json not found. API calls will fail.")
    except Exception as e:
        print(f"Error loading chatbetter.json: {e}")


def load_chatbetter_models():
    """Load ChatBetter models from models.json"""
    global CHATBETTER_MODELS
    try:
        with open("models.json", "r", encoding="utf-8") as f:
            CHATBETTER_MODELS = json.load(f)
            if not isinstance(CHATBETTER_MODELS, list):
                CHATBETTER_MODELS = []
                print("Warning: models.json should contain a list of model objects.")
                return
            print(f"Successfully loaded {len(CHATBETTER_MODELS)} models.")
    except FileNotFoundError:
        print("Error: models.json not found. Model list will be empty.")
        CHATBETTER_MODELS = []
    except Exception as e:
        print(f"Error loading models.json: {e}")
        CHATBETTER_MODELS = []


def get_best_chatbetter_account() -> Optional[ChatBetterAccount]:
    """Get the best available ChatBetter account using a smart selection algorithm."""
    with account_rotation_lock:
        now = time.time()
        valid_accounts = [
            acc for acc in CHATBETTER_ACCOUNTS
            if acc["is_valid"] and (
                acc["error_count"] < MAX_ERROR_COUNT or
                now - acc["last_used"] > ERROR_COOLDOWN
            )
        ]

        if not valid_accounts:
            return None

        # Reset error count for accounts that have been in cooldown
        for acc in valid_accounts:
            if acc["error_count"] >= MAX_ERROR_COUNT and now - acc["last_used"] > ERROR_COOLDOWN:
                acc["error_count"] = 0

        # Sort by last used (oldest first) and error count (lowest first)
        valid_accounts.sort(key=lambda x: (x["last_used"], x["error_count"]))
        account = valid_accounts[0]
        account["last_used"] = now
        return account


async def authenticate_client(
    auth: Optional[HTTPAuthorizationCredentials] = Depends(security),
):
    """Authenticate client based on API key in Authorization header"""
    if not VALID_CLIENT_KEYS:
        raise HTTPException(
            status_code=503,
            detail="Service unavailable: Client API keys not configured on server.",
        )

    if not auth or not auth.credentials:
        raise HTTPException(
            status_code=401,
            detail="API key required in Authorization header.",
            headers={"WWW-Authenticate": "Bearer"},
        )

    if auth.credentials not in VALID_CLIENT_KEYS:
        raise HTTPException(status_code=403, detail="Invalid client API key.")


@app.on_event("startup")
async def startup():
    """应用启动时初始化配置"""
    print("Starting ChatBetter OpenAI API Adapter server...")
    load_client_api_keys()
    load_chatbetter_accounts()
    load_chatbetter_models()
    print("Server initialization completed.")


def get_models_list_response() -> ModelList:
    """Helper to construct ModelList response from cached models."""
    model_infos = [
        ModelInfo(
            id=model.get("id", "unknown"),
            created=int(time.time()),
            owned_by=model.get("owned_by", "anthropic")
        )
        for model in CHATBETTER_MODELS
    ]
    return ModelList(data=model_infos)


@app.get("/v1/models", response_model=ModelList)
async def list_v1_models(_: None = Depends(authenticate_client)):
    """List available models - authenticated"""
    return get_models_list_response()


@app.get("/models", response_model=ModelList)
async def list_models_no_auth():
    """List available models without authentication - for client compatibility"""
    return get_models_list_response()


@app.get("/debug")
async def toggle_debug(enable: bool = Query(None)):
    """切换调试模式"""
    global DEBUG_MODE
    if enable is not None:
        DEBUG_MODE = enable
    return {"debug_mode": DEBUG_MODE}


@app.post("/v1/chat/completions")
async def chat_completions(
    request: ChatCompletionRequest, _: None = Depends(authenticate_client)
):
    """Create chat completion using ChatBetter backend"""
    # 直接获取完整的模型对象，而不是只提取ID
    model_item = next((m for m in CHATBETTER_MODELS if m.get("id") == request.model), None)
    if not model_item:
        raise HTTPException(status_code=404, detail=f"Model '{request.model}' not found.")

    if not request.messages:
        raise HTTPException(status_code=400, detail="No messages provided in the request.")

    log_debug(f"Processing request for model: {request.model}")

    # 尝试所有账户
    for attempt in range(len(CHATBETTER_ACCOUNTS)):
        account = get_best_chatbetter_account()
        if not account:
            raise HTTPException(
                status_code=503,
                detail="No valid ChatBetter accounts available."
            )

        try:
            # 使用与 testChat.py 相同的方式构建请求
            payload = {
                "stream": True,  # 始终从 ChatBetter API 获取流式响应
                "model": request.model,
                "messages": [
                    {"role": msg.role, "content": msg.content}
                    for msg in request.messages
                ],
                "params": {},
                "tool_servers": [],
                "model_item": model_item,  # 使用完整的模型对象
                "session_id": "",
                "chat_id": "",
                "id": "",
            }

            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
                "Accept-Encoding": "gzip, deflate, br, zstd",
                "Content-Type": "application/json",
                "authorization": f"Bearer {account['auth_token']}",
                "Cookie": f"ChatBetterJwt={account['accessToken']}",
            }

            log_debug(f"Sending request to ChatBetter API with account ending in ...{account['auth_token'][-4:]}")

            response = requests.post(
                "https://app.chatbetter.com/api/chat/completions",
                data=json.dumps(payload),
                headers=headers,
                stream=True,
                timeout=120.0,
            )
            response.raise_for_status()

            if request.stream:
                # 根据请求参数决定是返回原始响应还是转换后的响应
                if request.raw_response:
                    log_debug("Returning raw response stream")
                    return StreamingResponse(
                        raw_stream_generator(response),
                        media_type="text/event-stream",
                        headers={
                            "Cache-Control": "no-cache",
                            "Connection": "keep-alive",
                            "X-Accel-Buffering": "no",
                        },
                    )
                else:
                    log_debug("Returning processed response stream")
                    return StreamingResponse(
                        chatbetter_stream_generator(response, request.model),
                        media_type="text/event-stream",
                        headers={
                            "Cache-Control": "no-cache",
                            "Connection": "keep-alive",
                            "X-Accel-Buffering": "no",
                        },
                    )
            else:
                log_debug("Building non-stream response")
                return build_chatbetter_non_stream_response(response, request.model)

        except requests.HTTPError as e:
            status_code = getattr(e.response, "status_code", 500)
            error_detail = getattr(e.response, "text", str(e))
            print(f"ChatBetter API error ({status_code}): {error_detail}")

            with account_rotation_lock:
                if status_code in [401, 403]:
                    # 标记账户为无效
                    account["is_valid"] = False
                    print(f"Account ...{account['auth_token'][-4:]} marked as invalid due to auth error.")
                elif status_code in [429, 500, 502, 503, 504]:
                    # 增加错误计数
                    account["error_count"] += 1
                    print(f"Account ...{account['auth_token'][-4:]} error count: {account['error_count']}")
                else:
                    # 客户端错误，不尝试使用其他账户
                    if request.stream:
                        return StreamingResponse(
                            error_stream_generator(error_detail, status_code),
                            media_type="text/event-stream",
                            status_code=status_code,
                        )
                    else:
                        raise HTTPException(status_code=status_code, detail=error_detail)

        except Exception as e:
            print(f"Request error: {e}")
            with account_rotation_lock:
                account["error_count"] += 1

    # 所有尝试都失败
    if request.stream:
        return StreamingResponse(
            error_stream_generator("All attempts to contact ChatBetter API failed.", 503),
            media_type="text/event-stream",
            status_code=503,
        )
    else:
        raise HTTPException(status_code=503, detail="All attempts to contact ChatBetter API failed.")


def raw_stream_generator(response):
    """直接返回原始响应流，不做任何处理"""
    for chunk in response.iter_content(chunk_size=1024):
        if chunk:
            yield chunk.decode("utf-8")


def chatbetter_stream_generator(response, model: str):
    """Real-time streaming with format conversion - ChatBetter to OpenAI"""
    stream_id = f"chatcmpl-{uuid.uuid4().hex}"
    created_time = int(time.time())

    # 发送初始角色增量
    yield f"data: {StreamResponse(id=stream_id, created=created_time, model=model, choices=[StreamChoice(delta={'role': 'assistant'})]).json()}\n\n"

    buffer = ""
    try:
        for chunk in response.iter_content(chunk_size=1024):
            if not chunk:
                continue

            chunk_text = chunk.decode("utf-8")
            log_debug(f"Received chunk: {chunk_text[:100]}..." if len(chunk_text) > 100 else chunk_text)
            buffer += chunk_text

            # 处理缓冲区中的完整行
            while "\n" in buffer:
                line, buffer = buffer.split("\n", 1)
                line = line.strip()

                if not line.startswith("data:"):
                    continue

                data_str = line[5:].strip()
                if not data_str or data_str == "[DONE]":
                    if data_str == "[DONE]":
                        log_debug("Received DONE signal")
                    continue

                try:
                    data = json.loads(data_str)
                    choices = data.get("choices", [])
                    if not choices:
                        continue

                    delta = choices[0].get("delta", {})
                    openai_delta = {}

                    # 处理思考过程
                    if "reasoning_content" in delta:
                        openai_delta["reasoning_content"] = delta["reasoning_content"]
                        log_debug(f"Extracted reasoning_content: {delta['reasoning_content'][:50]}...")

                    # 处理正文内容
                    if "content" in delta:
                        openai_delta["content"] = delta["content"]
                        log_debug(f"Extracted content: {delta['content']}")

                    # 只有当有内容时才发送响应
                    if openai_delta:
                        openai_response = StreamResponse(
                            id=stream_id,
                            created=created_time,
                            model=model,
                            choices=[StreamChoice(delta=openai_delta)],
                        )
                        response_json = openai_response.json()
                        log_debug(f"Sending response: {response_json[:100]}...")
                        yield f"data: {response_json}\n\n"

                except json.JSONDecodeError as e:
                    log_debug(f"JSON decode error: {e}, data: {data_str[:100]}...")
                    continue
    except Exception as e:
        log_debug(f"Stream processing error: {e}")
        yield f"data: {json.dumps({'error': str(e)})}\n\n"
    finally:
        # 发送完成信号
        log_debug("Sending completion signal")
        yield f"data: {StreamResponse(id=stream_id, created=created_time, model=model, choices=[StreamChoice(delta={}, finish_reason='stop')]).json()}\n\n"
        yield "data: [DONE]\n\n"


def build_chatbetter_non_stream_response(response, model: str) -> ChatCompletionResponse:
    """Build non-streaming response by accumulating stream data."""
    full_content = ""
    full_reasoning_content = ""

    buffer = ""
    for chunk in response.iter_content(chunk_size=1024):
        if not chunk:
            continue

        buffer += chunk.decode("utf-8")

        while "\n" in buffer:
            line, buffer = buffer.split("\n", 1)
            line = line.strip()

            if not line.startswith("data:"):
                continue

            data_str = line[5:].strip()
            if not data_str or data_str == "[DONE]":
                continue

            try:
                data = json.loads(data_str)
                choices = data.get("choices", [])
                if not choices:
                    continue

                delta = choices[0].get("delta", {})
                if "reasoning_content" in delta:
                    full_reasoning_content += delta["reasoning_content"]
                if "content" in delta:
                    full_content += delta["content"]

            except json.JSONDecodeError:
                continue

    return ChatCompletionResponse(
        model=model,
        choices=[
            ChatCompletionChoice(
                message=ChatMessage(
                    role="assistant",
                    content=full_content,
                    reasoning_content=full_reasoning_content if full_reasoning_content else None,
                )
            )
        ],
    )


async def error_stream_generator(error_detail: str, status_code: int):
    """Generate error stream response"""
    yield f'data: {json.dumps({"error": {"message": error_detail, "type": "chatbetter_api_error", "code": status_code}})}\n\n'
    yield "data: [DONE]\n\n"


if __name__ == "__main__":
    import uvicorn

    # 设置环境变量以启用调试模式
    if os.environ.get("DEBUG_MODE", "").lower() == "true":
        DEBUG_MODE = True
        print("Debug mode enabled via environment variable")

    if not os.path.exists("chatbetter.json"):
        print("Warning: chatbetter.json not found. Creating a dummy file.")
        dummy_data = [
            {
                "auth_token": "your_auth_token_here",
                "accessToken": "your_access_token_here",
            }
        ]
        with open("chatbetter.json", "w", encoding="utf-8") as f:
            json.dump(dummy_data, f, indent=4)
        print("Created dummy chatbetter.json. Please replace with valid ChatBetter data.")

    if not os.path.exists("client_api_keys.json"):
        print("Warning: client_api_keys.json not found. Creating a dummy file.")
        dummy_key = f"sk-dummy-{uuid.uuid4().hex}"
        with open("client_api_keys.json", "w", encoding="utf-8") as f:
            json.dump([dummy_key], f, indent=2)
        print(f"Created dummy client_api_keys.json with key: {dummy_key}")

    if not os.path.exists("models.json"):
        print("Warning: models.json not found. Creating a dummy file.")
        dummy_models = [
            {
                "id": "claude-3.7-sonnet:thinking",
                "name": "Claude 3.7 Sonnet (thinking)",
                "owned_by": "anthropic"
            }
        ]
        with open("models.json", "w", encoding="utf-8") as f:
            json.dump(dummy_models, f, indent=4)
        print("Created dummy models.json.")

    load_client_api_keys()
    load_chatbetter_accounts()
    load_chatbetter_models()

    print("\n--- ChatBetter OpenAI API Adapter ---")
    print(f"Debug Mode: {DEBUG_MODE}")
    print("Endpoints:")
    print("  GET  /v1/models (Client API Key Auth)")
    print("  GET  /models (No Auth)")
    print("  POST /v1/chat/completions (Client API Key Auth)")
    print("  GET  /debug?enable=[true|false] (Toggle Debug Mode)")

    print(f"\nClient API Keys: {len(VALID_CLIENT_KEYS)}")
    if CHATBETTER_ACCOUNTS:
        print(f"ChatBetter Accounts: {len(CHATBETTER_ACCOUNTS)}")
    else:
        print("ChatBetter Accounts: None loaded. Check chatbetter.json.")
    if CHATBETTER_MODELS:
        models = sorted([m.get("id", "unknown") for m in CHATBETTER_MODELS])
        print(f"ChatBetter Models: {len(CHATBETTER_MODELS)}")
        print(f"Available models: {', '.join(models[:5])}{'...' if len(models) > 5 else ''}")
    else:
        print("ChatBetter Models: None loaded. Check models.json.")
    print("------------------------------------")

    uvicorn.run(app, host="0.0.0.0", port=8000)