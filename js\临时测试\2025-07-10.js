
import req from '../utils/request.js'

;(async () => {
    let codes = ['R7JU4OEN42OF','82N3CJ9GCE3C','9GJX28JPV49F','GHUTGU841FD5','F214128R4E1C','T3GAYLNQZD58','DHP6NDYQG282','G893K6EJA725','GPLTQVJFECKA','OQPNSSMIS775','GZ3TK8V1V62B','QNEI26459424','8O2OLEPPNN35F','2MQD44YJG7OF','8PJ6LSBZV6F1','SF52174BKDA7','U5NRZTGM1948','JJHKBTA5N215','ZUTJKE71011A','IO8TF2BUSE61','K63SFQSHA665','6E2IR5HCDDCA','14MFW8N3VC15','A5O668OMZFD7','F7E0918A93B3','9ITFXHCCT5A0C','MYQ6HW6FHC600','021UDCVFOA2E','MP3ZWOCHF9F2','N89A2FNUAAE','JAF56MW5P125','V6JRXEJ5J012','288UO7TTM68D','B9KXPM3WU747','AQ5AO8TC127D','D5PVUO4KKDF0','RIGO6S6U0561','R935MTYFR11C','JSG7FRGGSD9B','WTF7TBKVW8F6','NRTE9O9F4254','DYK6DJE2JC66','5FM882332013','QHF8UG533584','1362118PY733','8JTL7ICYZDF6','N8B3VCY2JE02','AB9F1GVEGF2C','T6DCVY3AV02A','1VG498NAZ298','BW6FRCQ1JB33','X315N4H0H4E6','PAKQ15DJT627','P74FOC6NH4D8','HPHR4LVB8C5D','YGH8JNKIJE16C','J8BEFOYX1E7D','K94KMYYSC0BD','CS8PGBT538DB','43H9GDDOG090','2DUB4A81FD61','UDTZS9DLO9AF','ZWO0CTRGPC7B','T3T69RK DZ221','NC81XTFN218','YXWB GADC2ABD','3OTT811A7BF','03F3XBB9X563','E1WJK40PBB3','TA9IUAS6004E','P7NV262031ED','UHAD49DQ9523','BEEZSXCX3J32','3M85EPUYBDE8','4QKYPIMCQ8C4','CV7KT3NNZ046','20M0EU2GA2D5','5185NLNJ338E','13WW24QONF52','TAU14Y3YW080','YMF483YC311C','AAF0IRBU4164','94U9Q6RJ506B','PPQ9O033E750','LJHRQR6LQA77','E6V4C307151F','FWDB2CORM1F4','AIOQOBO MKC31','L2AAR4KZO65C','185IDMQ4R4AE','RHOB9MVPV8FC','FCV9ZWDEY180','4F4V504C8B2A','AR6W4ZAAGD46','1UEM6C1NNA15','BPS5ZT0V8337','BKDYC8COFEB8','V62YAVXEP8AD','42C2SPHAOD53','R8U5Z9LM7AE3']
    for(let code of codes) {
        await wait()
         let resp =  await req.post('https://api.edgeone.ai/common/portal-api', {
            headers: {
                'Cookie': `sajssdk_2015_cross_new_user=1; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%22197f385e97d184-07487b0c6e6816-26011f51-2073600-197f385e97e8ca%22%2C%22first_id%22%3A%22%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E5%BC%95%E8%8D%90%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC%22%2C%22%24latest_referrer%22%3A%22https%3A%2F%2Fedgeone.ai%2Fdocument%2F70405%3Fproduct%3Dpricing%22%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTk3ZjM4NWU5N2QxODQtMDc0ODdiMGM2ZTY4MTYtMjYwMTFmNTEtMjA3MzYwMC0xOTdmMzg1ZTk3ZThjYSJ9%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A%22%22%7D%2C%22%24device_id%22%3A%22197f385e97d184-07487b0c6e6816-26011f51-2073600-197f385e97e8ca%22%7D`
            },
            json: {"Action":"redeem/collate","Data":{"code":code}}
        })
        console.log(code, resp)
    }

})();

async function wait(seconds = 1) {
    return new Promise((resolve) => {
        setTimeout(() => {
            resolve()
        }, seconds * 1000)
    })
}