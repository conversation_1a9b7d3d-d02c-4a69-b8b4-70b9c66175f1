#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LXC.WIKI 爬虫测试脚本
"""

import json
from datetime import datetime
from config import get_config

def test_config():
    """测试配置文件"""
    print("🧪 测试配置文件...")
    config = get_config()
    
    print(f"✓ 爬虫配置: {config['crawler']}")
    print(f"✓ 目标URL数量: {len(config['target_urls'])}")
    
    for i, url_config in enumerate(config['target_urls'], 1):
        if url_config.get('enabled', True):
            print(f"  {i}. {url_config['name']} - 取最后{url_config['take_last']}个分组")
        else:
            print(f"  {i}. {url_config['name']} - 已禁用")
    
    print(f"✓ 通知配置: {config['notification']}")
    print()

def test_single_url():
    """测试单个URL爬取"""
    print("🧪 测试单个URL爬取...")
    
    # 临时修改配置，只启用第一个URL
    from config import TARGET_URLS
    original_config = TARGET_URLS.copy()
    
    # 只启用第一个URL进行测试
    for i, url_config in enumerate(TARGET_URLS):
        url_config['enabled'] = (i == 0)
    
    try:
        from lxc.wiki import crawl_with_multithreading
        result = crawl_with_multithreading()
        print("✓ 单URL测试完成")
    except Exception as e:
        print(f"❌ 单URL测试失败: {e}")
    finally:
        # 恢复原始配置
        TARGET_URLS.clear()
        TARGET_URLS.extend(original_config)
    
    print()

def analyze_latest_data():
    """分析最新的爬取数据"""
    print("🧪 分析最新爬取数据...")
    
    import os
    import glob
    
    # 查找最新的数据文件
    data_files = glob.glob("lxc_wiki_data_*.json")
    if not data_files:
        print("❌ 未找到数据文件")
        return
    
    latest_file = max(data_files, key=os.path.getctime)
    print(f"📄 分析文件: {latest_file}")
    
    try:
        with open(latest_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"📊 数据统计:")
        print(f"  - 爬取时间: {data.get('crawl_time', '未知')}")
        print(f"  - URL源数量: {data.get('total_sources', 0)}")
        print(f"  - 总分组数: {data.get('total_groups', 0)}")
        print(f"  - 总产品数: {data.get('total_products', 0)}")
        print(f"  - 有库存产品: {data.get('total_available', 0)}")
        
        # 按源统计
        print(f"\n📋 按源统计:")
        for source in data.get('sources', []):
            print(f"  • {source['name']}: {source['group_count']} 个分组")
        
        # 统计有库存的产品
        available_products = []
        for group in data.get('groups', []):
            for product in group.get('products', []):
                if product.get('stock') and product['stock'] != '已售完':
                    available_products.append({
                        'name': product['name'],
                        'stock': product['stock'],
                        'price': product['price'],
                        'source': group.get('source_name', '未知')
                    })
        
        if available_products:
            print(f"\n🎉 发现 {len(available_products)} 个有库存产品:")
            for product in available_products:
                print(f"  • {product['name']} - {product['stock']} - {product['price']} ({product['source']})")
        else:
            print(f"\n😔 暂无有库存产品")
        
    except Exception as e:
        print(f"❌ 分析数据失败: {e}")
    
    print()

def test_notification():
    """测试通知功能"""
    print("🧪 测试通知功能...")
    
    try:
        from lxc.wiki import send_notification
        
        title = "LXC.WIKI 爬虫测试通知"
        content = f"""
这是一条测试通知消息。

测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
测试内容: 通知系统功能正常

如果您收到这条消息，说明通知配置正确。
        """.strip()
        
        send_notification(title, content)
        print("✓ 通知测试完成")
        
    except Exception as e:
        print(f"❌ 通知测试失败: {e}")
    
    print()

def main():
    """主测试函数"""
    print("🚀 LXC.WIKI 爬虫系统测试")
    print("=" * 50)
    
    # 测试配置
    test_config()
    
    # 分析最新数据
    analyze_latest_data()
    
    # 测试通知（可选）
    import sys
    if '--test-notify' in sys.argv:
        test_notification()
    
    print("✅ 测试完成!")
    print("\n使用说明:")
    print("  python test_crawler.py              - 运行基本测试")
    print("  python test_crawler.py --test-notify - 包含通知测试")

if __name__ == '__main__':
    main()
