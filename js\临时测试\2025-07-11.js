async function(data){
    //  处理每日重点事件 子项排序问题
    if(!this.inEdit){
        await document.querySelector('#yyjbxxq .h3-panel-body .pagination > ul .ant-pagination-options .ant-pagination-options-size-changer').click()
        await document.querySelector('#yyjbxxq .h3-panel-body .pagination > ul .ant-pagination-options .ant-select-dropdown .ant-select-dropdown-menu-item:nth-child(4)').click()

        let dataArr = data.yyjbxxq
        let parentArr = []
        let childArr = []
        dataArr.forEach(item => {
            if(!item.ReportParentId){
                parentArr.push(item)
            } else {
                childArr.push(item)
            }
        })
        parentArr = parentArr.sort((a, b) => {
            const dateA = Date.parse(a.Date1689733639084);
            const dateB = Date.parse(b.Date1689733639084);
            return dateA - dateB;
        })

        let childConvertArr = convert(childArr)
        console.log('childConvertArr', childConvertArr)
        childConvertArr.forEach(item => {
            item = item.sort((a, b) => {
                const dateA = Date.parse(a.Date1689733639084);
                const dateB = Date.parse(b.Date1689733639084);
                return dateA - dateB;
            })
        })
        // 数据重组
        for(let i = 0; i < childConvertArr.length; i++){
            let childDataID = childConvertArr[i][0].ReportParentId
            let index = parentArr.findIndex(item => item.id === childDataID)
            if(index !== -1){
                parentArr.splice(index + 1, 0, ...childConvertArr[i])
            }
        }

        const rowTemplate = document.querySelector('#yyjbxxq .sheet .sheet__body > .sheet__row')
        if(!rowTemplate) return
        const $sheet = document.querySelector('#yyjbxxq .sheet .sheet__body')
        $sheet.innerHTML = ''
        let parentIndex = 0
        for(let i = 0 ;i < parentArr.length ; i++) {
            let item = parentArr[i]
            let clone = rowTemplate.cloneNode(true)
            if(item.ReportParentId){
                clone.querySelector('& > .sheet__col span').className = 'Child'
                clone.querySelector('& > .sheet__col span').innerHTML = `${parentIndex}-${item.Sort}`
            } else {
                clone.querySelector('& > .sheet__col span').className = 'items'
                clone.querySelector('& > .sheet__col span').innerHTML = parentIndex + 1
                parentIndex ++
            }
            clone.querySelector('.sheet__cols .sheet__row .sheet__col:nth-child(2) > div').innerHTML = formatDateTime(item.Date1689733639084)
            clone.querySelector('.sheet__cols .sheet__row .sheet__col:nth-child(3) > div').innerHTML = item.Eventcontent
            clone.querySelector('.sheet__cols .sheet__row .sheet__col:nth-child(4) > div').innerHTML = item.Processingresults
            clone.querySelector('.sheet__cols .sheet__row .sheet__col:nth-child(5) > div').innerHTML = item.Informationstatus
            $sheet.appendChild(clone)
        }


    }
}