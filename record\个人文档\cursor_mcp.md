{"mcpServers": {"sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "interactive_feedback": {"command": "uvx", "args": ["mcp-feedback-enhanced@latest"], "timeout": 3600, "env": {"MCP_DESKTOP_MODE": "true", "MCP_WEB_PORT": "8765", "MCP_DEBUG": "false"}, "autoApprove": ["interactive_feedback"]}, "playwright": {"command": "cmd", "args": ["/c", "npx", "-y", "@executeautomation/playwright-mcp-server"]}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"]}}}