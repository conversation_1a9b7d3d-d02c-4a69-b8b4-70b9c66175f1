import httpx
import json
import random
import string
import time
import sys
import asyncio
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any, Optional
from fake_useragent import UserAgent
import requests

# 初始化UserAgent对象
ua = UserAgent()

class ProxyManager:
    """付费代理池管理器 - 支持API动态轮换"""

    def __init__(self):
        self.api_url = None
        self.api_working = False
        self.backup_proxies = []
        self.load_proxy_source()

    def load_proxy_source(self):
        """加载代理源 - API优先，文件备用"""
        print("🔄 正在验证FlashProxy API...")


        try:
            response = httpx.get(api_url, timeout=10, follow_redirects=True)

            if response.status_code == 200:
                proxy_line = response.text.strip()
                proxy_clean = ''.join(char for char in proxy_line if char.isprintable())

                if proxy_clean and len(proxy_clean) > 10:
                    parts = proxy_clean.split(':')
                    if len(parts) == 4:
                        self.api_url = api_url
                        self.api_working = True
                        print(f"✅ API验证成功，示例代理: {parts[0]}:{parts[1]}")
                        print("ℹ️ 将使用API动态轮换代理（每次请求自动换IP）")
                        print("✅ 代理源就绪")
                        return

            print("❌ API验证失败，尝试备用方案...")
            self.api_working = False
            self.load_backup_proxies()

        except Exception as e:
            print(f"❌ FlashProxy API请求失败: {e}")
            self.api_working = False
            print("🔄 尝试从本地文件加载备用代理...")
            self.load_backup_proxies()

    def load_backup_proxies(self):
        """加载备用代理文件"""
        try:
            if Path("100个proxy.txt").exists():
                with open("100个proxy.txt", 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        line_clean = ''.join(char for char in line if char.isprintable())
                        if line_clean and not line_clean.startswith('#'):
                            parts = line_clean.split(':')
                            if len(parts) == 4:
                                proxy_data = {
                                    'ip': parts[0],
                                    'port': parts[1],
                                    'username': parts[2],
                                    'password': parts[3],
                                    'full': line_clean
                                }
                                self.backup_proxies.append(proxy_data)
                print(f"✅ 从文件加载 {len(self.backup_proxies)} 个备用代理")
        except Exception as e:
            print(f"⚠️ 从文件加载代理失败: {e}")

    def get_random_proxy(self):
        """获取随机代理 - API优先"""
        if self.api_working and self.api_url:
            try:
                response = httpx.get(self.api_url, timeout=8, follow_redirects=True)
                if response.status_code == 200:
                    proxy_line = response.text.strip()
                    proxy_clean = ''.join(char for char in proxy_line if char.isprintable())

                    if proxy_clean:
                        parts = proxy_clean.split(':')
                        if len(parts) == 4:
                            return {
                                'ip': parts[0],
                                'port': parts[1],
                                'username': parts[2],
                                'password': parts[3],
                                'full': proxy_clean
                            }
            except Exception:
                print("⚠️ API获取代理失败，使用备用代理")

        # 使用备用代理
        if self.backup_proxies:
            return random.choice(self.backup_proxies)

        return None

    def remove_proxy(self, proxy):
        """移除无效代理（仅对备用代理有效）"""
        if proxy in self.backup_proxies:
            self.backup_proxies.remove(proxy)
            print(f"🗑️ 移除无效备用代理: {proxy['ip']}:{proxy['port']}")

    def get_proxy_count(self):
        """获取可用代理数量"""
        if self.api_working:
            return "API动态轮换"
        return len(self.backup_proxies)

# 全局代理管理器
proxy_manager = ProxyManager()

def generate_username(length=8):
    """生成随机用户名"""
    letters = string.ascii_lowercase + string.digits
    return ''.join(random.choice(letters) for _ in range(length))

def get_random_headers():
    """生成带有随机User-Agent的请求头"""
    return {
        'Content-Type': 'application/x-www-form-urlencoded',
        'accept': 'application/json',
        'accept-language': 'en-US,en;q=0.9',
        'origin': 'https://chutes.ai',
        'priority': 'u=1, i',
        'referer': 'https://chutes.ai/auth/start',
        'sec-ch-ua': '"Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'user-agent': ua.random,  # 随机User-Agent
        'x-sveltekit-action': 'true'
    }

def get_proxy_config():
    """获取代理配置"""
    proxy = proxy_manager.get_random_proxy()
    if proxy:
        print(f"🔄 使用代理: {proxy['ip']}:{proxy['port']}")
        return {
            'http': f'http://{proxy["username"]}:{proxy["password"]}@{proxy["ip"]}:{proxy["port"]}',
            'https': f'http://{proxy["username"]}:{proxy["password"]}@{proxy["ip"]}:{proxy["port"]}'
        }
    else:
        print("⚠️ 无可用代理，使用直连")
        return None

async def register_account_async(username: str, client: httpx.AsyncClient) -> Optional[Dict[str, Any]]:
    """Register a new account with the given username asynchronously."""
    url = "https://chutes.ai/auth/start?%2Fcreate="

    headers = get_random_headers()

    data = {
        'username': username,
    }

    try:
        response = await client.post(url, headers=headers, data=data)
        response.raise_for_status()
        return response.json()
    except httpx.HTTPStatusError as e:
        print(f"HTTP error occurred for {username}: {e}")
        return None
    except Exception as e:
        print(f"An error occurred for {username}: {e}")
        return None

def register_account(username):
    """同步注册函数，支持代理轮换"""
    url = "https://chutes.ai/auth/start?%2Fcreate="

    data = {
        'username': username,
    }

    # 尝试最多3次，每次使用不同代理
    max_attempts = 3
    for attempt in range(max_attempts):
        headers = get_random_headers()
        proxy = proxy_manager.get_random_proxy()

        try:
            if proxy:
                print(f"🔄 尝试 {attempt + 1}/{max_attempts} - 使用代理: {proxy['ip']}:{proxy['port']}")
                response = httpx.post(url, headers=headers, data=data, proxy=f'http://{proxy["username"]}:{proxy["password"]}@{proxy["ip"]}:{proxy["port"]}', timeout=20, verify=False)
            else:
                print(f"🔄 尝试 {attempt + 1}/{max_attempts} - 使用直连")
                response = httpx.post(url, headers=headers, data=data, timeout=20, verify=False)

            response.raise_for_status()
            return response.json()

        except httpx.HTTPStatusError as e:
            if e.response.status_code == 429:
                print(f"⚠️ 触发限流 429，当前代理: {proxy['ip']}:{proxy['port'] if proxy else '直连'}")
                if proxy:
                    proxy_manager.remove_proxy(proxy)
                if attempt < max_attempts - 1:
                    wait_time = random.uniform(5, 15)
                    print(f"等待 {wait_time:.1f} 秒后重试...")
                    time.sleep(wait_time)
                    continue
            else:
                print(f"HTTP错误 {e.response.status_code}: {e}")
                if proxy:
                    proxy_manager.remove_proxy(proxy)
                if attempt < max_attempts - 1:
                    continue
            return None

        except Exception as e:
            print(f"请求错误: {e}")
            if proxy:
                proxy_manager.remove_proxy(proxy)
            if attempt < max_attempts - 1:
                time.sleep(2)
                continue
            return None

    print(f"❌ {username} 注册失败，已尝试 {max_attempts} 次")
    return None

def extract_account_info(response_data):
    """Extract important account information from the registration response."""
    if not response_data or response_data.get("type") != "success":
        return None

    try:
        # Parse the data string from the response
        data_str = response_data.get("data", "")
        data = json.loads(data_str)

        # Based on the example response structure
        username = data[2]  # Username is at index 2
        user_id = data[3]   # User ID is at index 3
        created_at = data[5]  # Creation timestamp is at index 5
        hotkey = data[6]    # Hotkey is at index 6
        coldkey = data[7]   # Coldkey is at index 7
        api_key = data[10]['secret_key']  # API key is in a nested structure

        # Create a structured dictionary with the extracted information
        account_info = {
            "username": username,
            "user_id": user_id,
            "created_at": created_at,
            "hotkey": hotkey,
            "coldkey": coldkey,
            "api_key": api_key
        }

        return account_info
    except (json.JSONDecodeError, IndexError, KeyError, TypeError) as e:
        print(f"Error extracting account info: {e}")
        return None

def load_usernames_from_file(file_path):
    """从文本文件加载用户名"""
    try:
        with open(file_path, 'r') as f:
            # 读取行并去除空格，过滤空行
            usernames = [line.strip() for line in f if line.strip()]

        if not usernames:
            print("文件中没有找到有效的用户名。")
            return []

        print(f"从 {file_path} 加载了 {len(usernames)} 个用户名")
        return usernames
    except FileNotFoundError:
        print(f"错误: 文件 {file_path} 未找到。")
        return []
    except Exception as e:
        print(f"从文件加载用户名时出错: {e}")
        return []

def save_single_account(account_data):
    """实时保存单个账户"""
    output_dir = Path("registered_accounts")
    output_dir.mkdir(exist_ok=True)

    timestamp = datetime.now().strftime("%Y%m%d")
    output_file = output_dir / f"accounts_{timestamp}.json"

    # 读取现有数据
    existing_accounts = []
    if output_file.exists():
        try:
            with open(output_file, "r", encoding="utf-8") as f:
                existing_accounts = json.load(f)
        except:
            existing_accounts = []

    # 添加新账户
    existing_accounts.append(account_data)

    # 保存回文件
    with open(output_file, "w", encoding="utf-8") as f:
        json.dump(existing_accounts, f, indent=2, ensure_ascii=False)

    print(f"💾 账户已保存到 {output_file} (总计: {len(existing_accounts)} 个)")

def save_account_data(accounts):
    """将注册的账户数据保存到JSON文件"""
    output_dir = Path("registered_accounts")
    output_dir.mkdir(exist_ok=True)

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = output_dir / f"accounts_{timestamp}.json"

    with open(output_file, "w", encoding="utf-8") as f:
        json.dump(accounts, f, indent=2, ensure_ascii=False)

    print(f"已保存 {len(accounts)} 个账户到 {output_file}")

async def process_single_registration(username: str, client: httpx.AsyncClient) -> Optional[Dict[str, Any]]:
    """异步处理单个注册"""
    result = await register_account_async(username, client)

    if result and result.get("type") == "success":
        print(f"✅ 注册成功: {username}")

        # 提取并显示重要账户信息
        account_info = extract_account_info(result)
        if account_info:
            print(f"\n{username} 的账户信息:")
            print(f"  用户名: {account_info['username']}")
            print(f"  用户ID: {account_info['user_id']}")
            print(f"  API密钥: {account_info['api_key']}")

            account_data = {
                "username": username,
                "extracted_info": account_info,
                "full_response": result
            }
            return account_data
        else:
            print(f"⚠️ 无法从 {username} 的响应中提取账户信息")
            account_data = {
                "username": username,
                "registration_data": result
            }
            return account_data
    else:
        print(f"❌ 注册失败: {username}")
        return None

async def process_registrations_async(usernames: List[str], concurrency: int = 5, rate_limit: Optional[float] = None) -> List[Dict[str, Any]]:
    """异步批量处理用户名注册，带有并发控制"""
    successful_accounts = []

    print(f"开始并发注册，并发级别: {concurrency}")

    # 创建信号量限制并发请求
    semaphore = asyncio.Semaphore(concurrency)

    async def limited_register(username):
        async with semaphore:
            if rate_limit:
                # 如果指定了速率限制，应用它
                await asyncio.sleep(random.uniform(0, rate_limit))
            async with httpx.AsyncClient(timeout=30.0) as client:
                return await process_single_registration(username, client)

    # 为所有用户名创建任务
    tasks = [limited_register(username) for username in usernames]

    # 处理完成的结果
    for i, result_task in enumerate(asyncio.as_completed(tasks)):
        result = await result_task
        if result:
            successful_accounts.append(result)

    return successful_accounts

def process_registrations(usernames, delay_seconds):
    """批量处理用户名注册，带有智能随机延迟和实时保存"""
    successful_accounts = []

    for i, username in enumerate(usernames):
        print(f"正在注册账户 {i+1}/{len(usernames)}: {username}")

        result = register_account(username)

        if result and result.get("type") == "success":
            print(f"✅ 注册成功: {username}")

            # 提取并显示重要账户信息
            account_info = extract_account_info(result)
            if account_info:
                print("\n账户信息:")
                print(f"  用户名: {account_info['username']}")
                print(f"  用户ID: {account_info['user_id']}")
                print(f"  创建时间: {account_info['created_at']}")
                print(f"  API密钥: {account_info['api_key']}")
                print(f"  Coldkey: {account_info['coldkey']}")
                print(f"  Hotkey: {account_info['hotkey']}")

                account_data = {
                    "username": username,
                    "extracted_info": account_info,
                    "full_response": result
                }
                successful_accounts.append(account_data)

                # 🔥 实时保存 🔥
                save_single_account(account_data)
            else:
                print("⚠️ 无法从响应中提取账户信息")
                account_data = {
                    "username": username,
                    "registration_data": result
                }
                successful_accounts.append(account_data)

                # 🔥 实时保存 🔥
                save_single_account(account_data)
        else:
            print(f"❌ 注册失败: {username}")

        if i < len(usernames) - 1:
            # 减少延迟：基础延迟 + 1-3秒随机（因为使用多IP代理）
            random_extra = round(random.uniform(1.0, 2.0), 2)
            total_delay = delay_seconds + random_extra
            print(f"等待 {total_delay:.2f} 秒后进行下一次请求... (基础:{delay_seconds}s + 随机:{random_extra}s)")
            time.sleep(total_delay)

    return successful_accounts

async def main_async():
    print("Chutes.ai 账户注册工具")
    print("====================\n")

    # 选择注册模式
    print("注册模式:")
    print("1. 批量注册随机用户名")
    print("2. 注册自定义用户名 (手动输入)")
    print("3. 注册单个自定义用户名")
    print("4. 从文件加载用户名")
    print("5. 并发注册随机用户名")
    print("6. 并发注册自定义用户名")

    while True:
        try:
            mode = int(input("\n选择模式 (1-6): "))
            if 1 <= mode <= 6:
                break
            else:
                print("请输入1到6之间的数字。")
        except ValueError:
            print("请输入有效数字。")

    successful_accounts = []

    if mode == 1:
        # 批量注册随机用户名
        num_accounts = int(input("要注册的账户数量: "))
        delay_seconds = float(input("请求间隔延迟 (秒): "))

        usernames = [generate_username() for _ in range(num_accounts)]
        print(f"\n开始批量注册，将自动在每次请求间添加 2-5 秒随机延迟")
        successful_accounts = process_registrations(usernames, delay_seconds)

    elif mode == 2:
        # 手动输入自定义用户名注册
        print("\n请逐行输入用户名，输入空行结束。")
        usernames = []

        while True:
            username = input(f"用户名 {len(usernames) + 1}: ")
            if not username:
                if usernames:
                    break
                else:
                    print("请至少输入一个用户名。")
                    continue
            usernames.append(username)

        delay_seconds = float(input("请求间隔延迟 (秒): "))
        print(f"\n开始批量注册，将自动在每次请求间添加 2-5 秒随机延迟")
        successful_accounts = process_registrations(usernames, delay_seconds)

    elif mode == 3:
        # 注册单个自定义用户名
        username = input("输入用户名: ")

        print(f"正在注册账户: {username}")
        result = register_account(username)

        if result and result.get("type") == "success":
            print(f"✅ 注册成功: {username}")

            # 提取并显示重要账户信息
            account_info = extract_account_info(result)
            if account_info:
                print("\n账户信息:")
                print(f"  用户名: {account_info['username']}")
                print(f"  用户ID: {account_info['user_id']}")
                print(f"  创建时间: {account_info['created_at']}")
                print(f"  API密钥: {account_info['api_key']}")
                print(f"  Coldkey: {account_info['coldkey']}")
                print(f"  Hotkey: {account_info['hotkey']}")

                account_data = {
                    "username": username,
                    "extracted_info": account_info,
                    "full_response": result
                }
                successful_accounts.append(account_data)
            else:
                print("⚠️ 无法从响应中提取账户信息")
                account_data = {
                    "username": username,
                    "registration_data": result
                }
                successful_accounts.append(account_data)
        else:
            print(f"❌ 注册失败: {username}")

    elif mode == 4:
        # 从文件加载用户名
        file_path = input("输入包含用户名的文件路径 (每行一个): ")
        usernames = load_usernames_from_file(file_path)

        if usernames:
            delay_seconds = float(input("请求间隔延迟 (秒): "))
            print(f"\n开始批量注册，将自动在每次请求间添加 2-5 秒随机延迟")
            successful_accounts = process_registrations(usernames, delay_seconds)

    elif mode == 5:
        # 并发注册随机用户名
        num_accounts = int(input("要注册的账户数量: "))
        concurrency = int(input("并发请求数量 (建议5-10): "))
        rate_limit = float(input("请求间随机延迟 (秒, 输入0表示无延迟): ") or 0)

        if rate_limit <= 0:
            rate_limit = None

        usernames = [generate_username() for _ in range(num_accounts)]
        successful_accounts = await process_registrations_async(usernames, concurrency, rate_limit)

    elif mode == 6:
        # 并发注册自定义用户名
        source = input("来源 (1: 手动输入, 2: 文件): ")

        usernames = []
        if source == "1":
            print("\n请逐行输入用户名，输入空行结束。")
            while True:
                username = input(f"用户名 {len(usernames) + 1}: ")
                if not username:
                    if usernames:
                        break
                    else:
                        print("请至少输入一个用户名。")
                        continue
                usernames.append(username)
        else:
            file_path = input("输入包含用户名的文件路径 (每行一个): ")
            usernames = load_usernames_from_file(file_path)

        if usernames:
            concurrency = int(input("并发请求数量 (建议5-10): "))
            rate_limit = float(input("请求间随机延迟 (秒, 输入0表示无延迟): ") or 0)

            if rate_limit <= 0:
                rate_limit = None

            successful_accounts = await process_registrations_async(usernames, concurrency, rate_limit)

    if successful_accounts:
        save_account_data(successful_accounts)

    print(f"\n注册完成。成功注册了 {len(successful_accounts)} 个账户。")

def main():
    asyncio.run(main_async())

if __name__ == "__main__":
    main()