import requests
from bs4 import BeautifulSoup
import json
import time
from urllib.parse import urljoin
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
from datetime import datetime
import os
import sys

# 导入配置和通知模块
try:
    from config import get_config, update_config_from_env, NOTIFY_SERVICES
    CONFIG = get_config()
    update_config_from_env()
except ImportError:
    print("警告: 未找到config.py模块，使用默认配置")
    CONFIG = {
        'crawler': {'max_workers': 3, 'request_delay': 2, 'save_history': True, 'history_file': 'lxc_wiki_history.json'},
        'notification': {'enable_notifications': True, 'notify_on_stock_change': True, 'notify_on_available_stock': True},
        'notify_services': {'CONSOLE': True},
        'monitor': {'check_interval': 300},
        'filter': {}
    }
    NOTIFY_SERVICES = {'CONSOLE': True}

try:
    from notify import send as notify_send
    NOTIFY_AVAILABLE = True
except ImportError:
    print("警告: 未找到notify.py模块，通知功能将被禁用")
    NOTIFY_AVAILABLE = False

# 线程锁
print_lock = threading.Lock()
data_lock = threading.Lock()

def safe_print(message):
    """线程安全的打印函数"""
    with print_lock:
        print(message)

def load_history():
    """加载历史数据"""
    if not CONFIG['crawler']['save_history']:
        return {}

    try:
        history_file = CONFIG['crawler']['history_file']
        if os.path.exists(history_file):
            with open(history_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}
    except Exception as e:
        safe_print(f"加载历史数据失败: {e}")
        return {}

def save_history(data):
    """保存历史数据"""
    if not CONFIG['crawler']['save_history']:
        return

    try:
        history_file = CONFIG['crawler']['history_file']
        with open(history_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
    except Exception as e:
        safe_print(f"保存历史数据失败: {e}")

def check_stock_change(product, history_data):
    """检查库存状态是否变化"""
    if not history_data:
        return False

    # 查找历史记录中的产品
    product_key = f"{product['group_name']}_{product['name']}"
    if product_key in history_data:
        old_stock = history_data[product_key].get('stock', '')
        new_stock = product.get('stock', '')
        # 如果库存状态从"已售完"变为其他状态，或从其他状态变为"已售完"
        if old_stock != new_stock:
            return True
    return False

def send_notification(title, content):
    """发送通知"""
    if not CONFIG['notification']['enable_notifications'] or not NOTIFY_AVAILABLE:
        return

    try:
        # 使用配置的通知服务
        notify_send(title, content, **NOTIFY_SERVICES)
        safe_print(f"✅ 通知已发送: {title}")
    except Exception as e:
        safe_print(f"❌ 发送通知失败: {e}")


def fetch_secondgroup_items(url_config):
    """
    爬取指定URL中所有class为secondgroup_item的节点，提取a标签的href和innerHTML
    并根据配置只取最后N个分组

    Args:
        url_config: 目标URL配置字典，包含url、name、take_last等信息

    Returns:
        包含href和text的字典列表，以及URL配置信息
    """
    url = url_config['url']
    take_last = url_config.get('take_last', 0)  # 默认取所有分组
    url_name = url_config.get('name', '未命名')

    # 设置请求头，模拟浏览器行为
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Referer': 'https://www.lxc.wiki/'
    }

    try:
        # 发送HTTP请求
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()  # 如果请求不成功则抛出异常

        # 使用BeautifulSoup解析HTML
        soup = BeautifulSoup(response.text, 'html.parser')

        # 查找所有class为secondgroup_item的节点
        items = soup.find_all(class_='secondgroup_item')

        # 如果配置了只取最后N个分组，则进行切片
        if take_last > 0 and take_last < len(items):
            safe_print(f"URL '{url_name}': 找到 {len(items)} 个分组，根据配置只取最后 {take_last} 个")
            items = items[-take_last:]

        result = []
        for item in items:
            # 从每个节点中提取a标签
            a_tag = item.find('a')
            if a_tag:
                # 获取href属性和innerHTML内容
                href = a_tag.get('href', '')
                # 将相对URL转换为绝对URL
                if href and not href.startswith(('http://', 'https://')):
                    href = urljoin(url, href)

                text = a_tag.get_text(strip=True)

                result.append({
                    'href': href,
                    'text': text,
                    'source_url': url,
                    'source_name': url_name
                })

        return result

    except requests.exceptions.RequestException as e:
        safe_print(f"请求错误 {url_name} ({url}): {e}")
        return []
    except Exception as e:
        safe_print(f"解析错误 {url_name} ({url}): {e}")
        return []


def fetch_product_details(group_info, history_data=None):
    """
    爬取指定URL中的产品详情信息，并检测库存变化

    Args:
        group_info: 包含分组信息的字典 {'text': '分组名', 'href': 'URL', 'source_url': '来源URL', 'source_name': '来源名称'}
        history_data: 历史数据字典

    Returns:
        包含产品详情的字典，包括分组信息和产品列表
    """
    url = group_info['href']
    group_name = group_info['text']
    source_url = group_info.get('source_url', '')
    source_name = group_info.get('source_name', '未知来源')

    # 设置请求头，模拟浏览器行为
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Referer': 'https://www.lxc.wiki/'
    }

    try:
        # 添加请求延迟
        time.sleep(CONFIG['crawler']['request_delay'])

        # 发送HTTP请求
        response = requests.get(url, headers=headers, timeout=15)
        response.raise_for_status()

        # 使用BeautifulSoup解析HTML
        soup = BeautifulSoup(response.text, 'html.parser')

        # 查找产品容器
        products_container = soup.find(class_='products-container')
        if not products_container:
            safe_print(f"未找到products-container: {url}")
            return {
                'group_name': group_name,
                'group_url': url,
                'products': [],
                'product_count': 0,
                'error': 'products-container not found'
            }

        # 查找所有产品项
        product_items = products_container.find_all(class_='product-item')

        products = []
        available_products = []  # 有库存的产品

        for item in product_items:
            product = {}

            # 提取产品名称
            name_elem = item.find(class_='product-name')
            product['name'] = name_elem.get_text(strip=True) if name_elem else ''

            # 提取产品描述
            desc_elem = item.find(class_='product-description-container')
            product['description'] = desc_elem.get_text(strip=True) if desc_elem else ''

            # 提取库存状态
            stock_elem = item.find(class_='stock-indicator')
            product['stock'] = stock_elem.get_text(strip=True) if stock_elem else ''

            # 提取价格信息
            price_elem = item.find(class_='price-container')
            product['price'] = price_elem.get_text(strip=True) if price_elem else ''

            # 添加分组信息
            product['group_name'] = group_name
            product['group_url'] = url

            # 只添加有效的产品信息
            if product['name'] or product['description']:
                products.append(product)

                # 检查库存状态
                if product['stock'] and product['stock'] != '已售完':
                    available_products.append(product)

                    # 检查库存变化并发送通知
                    if CONFIG['notification']['notify_on_stock_change'] and history_data:
                        product_key = f"{group_name}_{product['name']}"
                        if product_key in history_data:
                            old_stock = history_data[product_key].get('stock', '')
                            if old_stock == '已售完' and product['stock'] != '已售完':
                                # 库存从已售完变为有库存，发送通知
                                title = f"🎉 LXC.WIKI 库存补货通知"
                                content = f"""
产品名称: {product['name']}
分组: {group_name}
当前库存: {product['stock']}
价格: {product['price']}
描述: {product['description'][:100]}...

链接: {url}
时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
                                """.strip()
                                send_notification(title, content)

        # 如果有可用产品，发送汇总通知
        if available_products and CONFIG['notification']['notify_on_available_stock']:
            title = f"📦 LXC.WIKI 有库存产品 - {group_name}"
            content = f"发现 {len(available_products)} 个有库存的产品:\n\n"
            for prod in available_products:
                content += f"• {prod['name']} - {prod['stock']} - {prod['price']}\n"
            content += f"\n链接: {url}"
            send_notification(title, content)

        safe_print(f"✓ {source_name} - {group_name}: 找到 {len(products)} 个产品，其中 {len(available_products)} 个有库存")

        return {
            'group_name': group_name,
            'group_url': url,
            'source_url': source_url,
            'source_name': source_name,
            'products': products,
            'product_count': len(products),
            'available_count': len(available_products)
        }

    except requests.exceptions.RequestException as e:
        safe_print(f"请求错误 {source_name} - {group_name} ({url}): {e}")
        return {
            'group_name': group_name,
            'group_url': url,
            'source_url': source_url,
            'source_name': source_name,
            'products': [],
            'product_count': 0,
            'available_count': 0,
            'error': str(e)
        }
    except Exception as e:
        safe_print(f"解析错误 {source_name} - {group_name} ({url}): {e}")
        return {
            'group_name': group_name,
            'group_url': url,
            'source_url': source_url,
            'source_name': source_name,
            'products': [],
            'product_count': 0,
            'available_count': 0,
            'error': str(e)
        }


def crawl_with_multithreading():
    """使用多线程爬取所有分组的产品信息"""
    target_urls = CONFIG.get('target_urls', [])

    if not target_urls:
        safe_print("❌ 错误: 未配置目标URL")
        return

    # 过滤出启用的URL
    enabled_urls = [url for url in target_urls if url.get('enabled', True)]

    if not enabled_urls:
        safe_print("❌ 错误: 所有目标URL都已禁用")
        return

    safe_print(f"🚀 开始多线程爬取 {len(enabled_urls)} 个目标URL")
    safe_print(f"配置: 最大并发数={CONFIG['crawler']['max_workers']}, 请求间隔={CONFIG['crawler']['request_delay']}秒")

    # 加载历史数据
    history_data = load_history()

    # 获取所有URL的分组列表
    all_items = []
    for url_config in enabled_urls:
        safe_print(f"📡 正在获取 '{url_config['name']}' 的分组列表...")
        items = fetch_secondgroup_items(url_config)
        if items:
            all_items.extend(items)
            safe_print(f"✓ '{url_config['name']}': 获取到 {len(items)} 个分组")
        else:
            safe_print(f"⚠️ '{url_config['name']}': 未找到任何分组")

    if not all_items:
        safe_print("❌ 所有URL都未找到任何符合条件的节点")
        return

    safe_print(f"📋 总共找到 {len(all_items)} 个分组")

    # 存储完整的数据结构
    complete_data = {
        'sources': [],  # 存储每个URL源的信息
        'groups': [],
        'total_sources': len(enabled_urls),
        'total_groups': len(all_items),
        'total_products': 0,
        'total_available': 0,
        'crawl_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'config': {
            'crawler': CONFIG['crawler'].copy(),
            'notification': CONFIG['notification'].copy()
        }
    }

    # 为每个URL源创建信息
    for url_config in enabled_urls:
        source_info = {
            'name': url_config['name'],
            'url': url_config['url'],
            'group_count': len([item for item in all_items if item.get('source_name') == url_config['name']]),
            'take_last': url_config.get('take_last', 0)
        }
        complete_data['sources'].append(source_info)

    # 使用线程池并发爬取
    with ThreadPoolExecutor(max_workers=CONFIG['crawler']['max_workers']) as executor:
        # 提交所有任务
        future_to_group = {
            executor.submit(fetch_product_details, item, history_data): item
            for item in all_items
        }

        # 收集结果
        completed = 0
        for future in as_completed(future_to_group):
            group_info = future_to_group[future]
            completed += 1

            try:
                group_data = future.result()

                with data_lock:
                    complete_data['groups'].append(group_data)
                    complete_data['total_products'] += group_data['product_count']
                    complete_data['total_available'] += group_data.get('available_count', 0)

                safe_print(f"📊 进度: {completed}/{len(items)} - {group_info['text']}")

            except Exception as e:
                safe_print(f"❌ 处理分组 {group_info['text']} 时出错: {e}")

    # 更新历史数据
    new_history = {}
    for group in complete_data['groups']:
        for product in group['products']:
            product_key = f"{product['group_name']}_{product['name']}"
            new_history[product_key] = {
                'stock': product['stock'],
                'price': product['price'],
                'last_update': complete_data['crawl_time']
            }

    save_history(new_history)

    # 统计有库存的产品，按源分组
    available_products = []
    available_by_source = {}

    for group in complete_data['groups']:
        source_name = group.get('source_name', '未知来源')
        if source_name not in available_by_source:
            available_by_source[source_name] = []

        for product in group['products']:
            if product['stock'] and product['stock'] != '已售完':
                product_info = {
                    'name': product['name'],
                    'stock': product['stock'],
                    'price': product['price'],
                    'group_name': group['group_name'],
                    'source_name': source_name,
                    'group_url': group['group_url']
                }
                available_products.append(product_info)
                available_by_source[source_name].append(product_info)

    # 输出统计信息
    safe_print(f"\n🎉 爬取完成!")
    safe_print(f"📈 统计信息:")
    safe_print(f"  - 总URL源: {complete_data['total_sources']}")
    safe_print(f"  - 总分组数: {complete_data['total_groups']}")
    safe_print(f"  - 总产品数: {complete_data['total_products']}")
    safe_print(f"  - 有库存产品: {len(available_products)}")

    # 按源显示有库存产品数量
    for source_name, products in available_by_source.items():
        if products:
            safe_print(f"    • {source_name}: {len(products)} 个有库存")


    # 发送汇总通知
    if available_products and CONFIG['notification']['notify_summary']:
        max_show = CONFIG['notification'].get('max_products_in_summary', 5)
        title = f"🛒 LXC.WIKI 库存汇总 ({len(available_products)}个有库存)"
        content = f"爬取时间: {complete_data['crawl_time']}\n"
        content += f"总计: {complete_data['total_products']} 个产品，{len(available_products)} 个有库存\n\n"

        # 按源分组显示有库存产品
        for source_name, products in available_by_source.items():
            if products:
                content += f"【{source_name}】({len(products)}个):\n"
                # 每个源最多显示max_show个产品
                for i, product in enumerate(products[:max_show], 1):
                    content += f"{i}. {product['name']} - {product['stock']} - {product['price']}\n"
                if len(products) > max_show:
                    content += f"... 还有 {len(products) - max_show} 个产品有库存\n"
                content += "\n"

        send_notification(title, content)

    return complete_data


if __name__ == '__main__':
    try:
        # 可以通过命令行参数调整配置
        if len(sys.argv) > 1:
            if sys.argv[1] == '--no-notify':
                CONFIG['notification']['enable_notifications'] = False
                safe_print("已禁用通知功能")
            elif sys.argv[1] == '--fast':
                CONFIG['crawler']['max_workers'] = 10
                CONFIG['crawler']['request_delay'] = 0.5
                safe_print("已启用快速模式: 10线程, 0.5秒延迟")
            elif sys.argv[1] == '--monitor':
                # 持续监控模式
                interval = CONFIG['monitor']['check_interval']
                safe_print(f"启动持续监控模式，间隔 {interval} 秒")
                while True:
                    try:
                        crawl_with_multithreading()
                        safe_print(f"等待 {interval} 秒后进行下一次检查...")
                        time.sleep(interval)
                    except Exception as e:
                        safe_print(f"监控过程中发生错误: {e}")
                        time.sleep(60)  # 出错后等待1分钟再重试
            elif sys.argv[1] == '--help':
                safe_print("""
LXC.WIKI 爬虫使用说明:
  python lxc.wiki.py             - 正常模式运行
  python lxc.wiki.py --no-notify - 禁用通知运行
  python lxc.wiki.py --fast      - 快速模式运行
  python lxc.wiki.py --monitor   - 持续监控模式
  python lxc.wiki.py --help      - 显示帮助信息
                """)
                sys.exit(0)

        # 正常模式运行一次
        crawl_with_multithreading()

    except KeyboardInterrupt:
        safe_print("\n⚠️  用户中断爬取")
    except Exception as e:
        safe_print(f"❌ 爬取过程中发生错误: {e}")
        if CONFIG['notification']['enable_notifications']:
            send_notification("LXC.WIKI 爬虫错误", f"爬取过程中发生错误: {e}")
