// 请求监控组件

import { requestInterceptor, RequestInfo } from '../utils/interceptor';
import { <PERSON><PERSON>, TabContainer } from './Modal';

export interface RequestMonitorEvents {
  onShowMessage: (message: string, type: 'success' | 'error' | 'warning') => void;
}

export class RequestMonitor {
  private container: HTMLElement;
  private events: RequestMonitorEvents;

  constructor(events: RequestMonitorEvents) {
    this.events = events;
    this.container = this.createElement();
    this.bindEvents();
    this.setupRequestListener();

    // 初始化时更新一次列表
    this.updateRequestList();
  }

  /**
   * 创建请求监控元素
   */
  private createElement(): HTMLElement {
    const container = document.createElement('div');
    container.className = 'request-monitor';
    container.innerHTML = this.getTemplate();
    return container;
  }

  /**
   * 获取HTML模板
   */
  private getTemplate(): string {
    return `
      <div class="monitor-header">
        <div class="monitor-stats">
          <div class="stat-item">
            <span class="stat-dot success"></span>
            <span id="success-count">0</span> 成功
          </div>
          <div class="stat-item">
            <span class="stat-dot error"></span>
            <span id="error-count">0</span> 失败
          </div>
          <div class="stat-item">
            <span class="stat-dot pending"></span>
            <span id="pending-count">0</span> 进行中
          </div>
        </div>
        <div class="monitor-controls">
          <button class="btn btn-sm btn-secondary" id="clear-requests-btn">清空</button>
          <button class="btn btn-sm btn-secondary" id="export-requests-btn">导出</button>
        </div>
      </div>

      <div class="request-filters">
        <input type="text" class="filter-input" id="filter-url" placeholder="过滤URL...">
        <select class="filter-select" id="filter-method">
          <option value="">所有方法</option>
          <option value="GET">GET</option>
          <option value="POST">POST</option>
          <option value="PUT">PUT</option>
          <option value="DELETE">DELETE</option>
          <option value="PATCH">PATCH</option>
        </select>
        <select class="filter-select" id="filter-status">
          <option value="">所有状态</option>
          <option value="success">成功</option>
          <option value="error">失败</option>
          <option value="pending">进行中</option>
        </select>
      </div>

      <div class="request-list" id="request-list">
        <div class="empty-state">
          <div class="empty-icon">📡</div>
          <div class="empty-text">暂无请求记录</div>
        </div>
      </div>
    `;
  }

  /**
   * 绑定事件
   */
  private bindEvents(): void {
    const clearBtn = this.container.querySelector('#clear-requests-btn') as HTMLButtonElement;
    const exportBtn = this.container.querySelector('#export-requests-btn') as HTMLButtonElement;
    const filterUrl = this.container.querySelector('#filter-url') as HTMLInputElement;
    const filterMethod = this.container.querySelector('#filter-method') as HTMLSelectElement;
    const filterStatus = this.container.querySelector('#filter-status') as HTMLSelectElement;

    // 清空请求
    clearBtn.addEventListener('click', () => {
      requestInterceptor.clearRequests();
      this.updateRequestList();
    });

    // 导出请求
    exportBtn.addEventListener('click', () => {
      this.exportRequests();
    });

    // 过滤器变化
    [filterUrl, filterMethod, filterStatus].forEach(filter => {
      filter.addEventListener('input', () => {
        this.updateRequestList();
      });
    });
  }

  /**
   * 设置请求监听器
   */
  private setupRequestListener(): void {
    requestInterceptor.addListener((request) => {
      console.log('收到请求更新:', request);
      // 使用setTimeout确保DOM更新
      setTimeout(() => {
        this.updateRequestList();
      }, 0);
    });
  }

  /**
   * 更新请求列表
   */
  private updateRequestList(): void {
    const requests = this.getFilteredRequests();
    const stats = requestInterceptor.getStats();
    const requestList = this.container.querySelector('#request-list') as HTMLElement;

    console.log('更新请求列表:', {
      requestsCount: requests.length,
      stats,
      hasRequestList: !!requestList
    });

    if (!requestList) {
      console.error('找不到请求列表容器');
      return;
    }

    // 更新统计信息
    this.updateRequestStats(stats);

    // 更新请求列表
    if (requests.length === 0) {
      requestList.innerHTML = `
        <div class="empty-state">
          <div class="empty-icon">📡</div>
          <div class="empty-text">暂无请求记录</div>
        </div>
      `;
      return;
    }

    requestList.innerHTML = requests.map(request => this.createRequestItem(request)).join('');

    // 绑定请求项点击事件
    requestList.querySelectorAll('.request-item').forEach((item, index) => {
      item.addEventListener('click', () => {
        this.showRequestDetail(requests[index]);
      });
    });
  }

  /**
   * 获取过滤后的请求
   */
  private getFilteredRequests(): RequestInfo[] {
    const allRequests = requestInterceptor.getRequests();
    console.log('所有请求数量:', allRequests.length);

    const filterUrl = (this.container.querySelector('#filter-url') as HTMLInputElement)?.value?.toLowerCase() || '';
    const filterMethod = (this.container.querySelector('#filter-method') as HTMLSelectElement)?.value || '';
    const filterStatus = (this.container.querySelector('#filter-status') as HTMLSelectElement)?.value || '';

    const filteredRequests = allRequests.filter(request => {
      // URL过滤
      if (filterUrl && !request.url.toLowerCase().includes(filterUrl)) {
        return false;
      }

      // 方法过滤
      if (filterMethod && request.method !== filterMethod) {
        return false;
      }

      // 状态过滤
      if (filterStatus) {
        const status = this.getRequestStatus(request);
        if (status !== filterStatus) {
          return false;
        }
      }

      return true;
    });

    console.log('过滤后请求数量:', filteredRequests.length);
    return filteredRequests;
  }

  /**
   * 获取请求状态
   */
  private getRequestStatus(request: RequestInfo): string {
    if (request.error) return 'error';
    if (!request.status) return 'pending';
    if (request.status >= 200 && request.status < 300) return 'success';
    if (request.status >= 400) return 'error';
    return 'success';
  }

  /**
   * 更新请求统计
   */
  private updateRequestStats(stats: { total: number; success: number; error: number; pending: number }): void {
    const successCount = this.container.querySelector('#success-count') as HTMLElement;
    const errorCount = this.container.querySelector('#error-count') as HTMLElement;
    const pendingCount = this.container.querySelector('#pending-count') as HTMLElement;

    successCount.textContent = stats.success.toString();
    errorCount.textContent = stats.error.toString();
    pendingCount.textContent = stats.pending.toString();
  }

  /**
   * 创建请求项HTML
   */
  private createRequestItem(request: RequestInfo): string {
    const status = this.getRequestStatus(request);
    const statusClass = status === 'success' ? 'success' : status === 'error' ? 'error' : 'pending';
    const statusText = request.status ? request.status.toString() : '进行中';
    const duration = request.duration ? `${request.duration}ms` : '-';
    const size = request.size ? this.formatBytes(request.size) : '-';
    const time = new Date(request.timestamp).toLocaleTimeString();

    return `
      <div class="request-item">
        <div class="request-header">
          <span class="request-method ${request.method}">${request.method}</span>
          <div class="request-status">
            <span class="status-code ${statusClass}">${statusText}</span>
            <span class="request-time">${time}</span>
          </div>
        </div>
        <div class="request-url">${request.url}</div>
        <div class="request-meta">
          <span class="request-size">${size}</span>
          <span class="request-duration">${duration}</span>
        </div>
      </div>
    `;
  }

  /**
   * 格式化字节大小
   */
  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * 显示请求详情
   */
  private showRequestDetail(request: RequestInfo): void {
    const detailModal = new Modal({
      title: `请求详情 - ${request.method}`,
      width: 700,
      height: 600,
      showTitle: true // 明确设置显示标题
    });

    const tabContainer = new TabContainer();

    // 为详情弹窗添加专门的CSS类
    const modalElement = detailModal.getModal();
    modalElement.classList.add('request-detail-modal');

    // 基本信息标签页
    const generalInfo = this.createGeneralInfoTab(request);
    tabContainer.addTab('general', '基本信息', generalInfo);

    // 请求头标签页
    if (Object.keys(request.headers).length > 0) {
      const requestHeaders = this.createHeadersTab(request.headers);
      tabContainer.addTab('request-headers', '请求头', requestHeaders);
    }

    // 响应头标签页
    if (request.responseHeaders && Object.keys(request.responseHeaders).length > 0) {
      const responseHeaders = this.createHeadersTab(request.responseHeaders);
      tabContainer.addTab('response-headers', '响应头', responseHeaders);
    }

    // 请求体标签页
    if (request.body) {
      const requestBody = this.createBodyTab(request.body);
      tabContainer.addTab('request-body', '请求体', requestBody);
    }

    // 响应体标签页
    if (request.responseBody) {
      const responseBody = this.createBodyTab(request.responseBody);
      tabContainer.addTab('response-body', '响应体', responseBody);
    }

    detailModal.setContent(tabContainer.getContainer());
    detailModal.show();
  }

  /**
   * 创建基本信息标签页
   */
  private createGeneralInfoTab(request: RequestInfo): HTMLElement {
    const container = document.createElement('div');
    container.className = 'detail-content';

    const info = [
      ['URL', request.url],
      ['方法', request.method],
      ['状态码', request.status ? request.status.toString() : '进行中'],
      ['状态文本', request.statusText || '-'],
      ['请求时间', new Date(request.timestamp).toLocaleString()],
      ['响应时间', request.duration ? `${request.duration}ms` : '-'],
      ['响应大小', request.size ? this.formatBytes(request.size) : '-'],
      ['错误信息', request.error || '-']
    ];

    container.innerHTML = `
      <div class="key-value-list">
        ${info.map(([key, value]) => `
          <div class="key-value-item">
            <div class="key">${key}:</div>
            <div class="value">${value}</div>
          </div>
        `).join('')}
      </div>
    `;

    return container;
  }

  /**
   * 创建头部信息标签页
   */
  private createHeadersTab(headers: Record<string, string>): HTMLElement {
    const container = document.createElement('div');
    container.className = 'detail-content';

    container.innerHTML = `
      <div class="key-value-list">
        ${Object.entries(headers).map(([key, value]) => `
          <div class="key-value-item">
            <div class="key">${key}:</div>
            <div class="value">${value}</div>
          </div>
        `).join('')}
      </div>
    `;

    return container;
  }

  /**
   * 创建请求/响应体标签页
   */
  private createBodyTab(body: any): HTMLElement {
    const container = document.createElement('div');
    container.className = 'detail-content';

    let content = '';
    if (typeof body === 'string') {
      try {
        // 尝试格式化JSON
        const parsed = JSON.parse(body);
        content = JSON.stringify(parsed, null, 2);
      } catch {
        content = body;
      }
    } else {
      content = JSON.stringify(body, null, 2);
    }

    container.innerHTML = `
      <div class="detail-section">
        <div class="section-content">
          <pre>${content}</pre>
        </div>
      </div>
    `;

    return container;
  }

  /**
   * 导出请求数据
   */
  private exportRequests(): void {
    const requests = requestInterceptor.getRequests();
    const data = {
      exportTime: new Date().toISOString(),
      totalRequests: requests.length,
      requests: requests
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `requests_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    this.events.onShowMessage('请求数据已导出', 'success');
  }

  /**
   * 获取容器元素
   */
  getContainer(): HTMLElement {
    return this.container;
  }

  /**
   * 销毁组件
   */
  destroy(): void {
    if (this.container.parentNode) {
      this.container.parentNode.removeChild(this.container);
    }
  }
}
