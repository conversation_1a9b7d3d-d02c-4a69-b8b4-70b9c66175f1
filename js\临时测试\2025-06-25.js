async function u(t) {
    let e = new TextEncoder().encode(t)
    return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256", e))).map(t => t.toString(16).padStart(2, "0")).join("")
}
async function c() {
    let t = Date.now()
        , e = Math.random().toString(36).substring(2, 15)
        , r = "".concat(t, ":").concat(e)
        , o = (await u(r)).substring(0, 16)
        , n = "".concat(t, ":").concat(e, ":").concat(o)

    // return base64
    return btoa(n)

}

console.log(await c());