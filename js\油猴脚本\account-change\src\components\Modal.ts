// 模态弹窗组件

export interface ModalOptions {
  title?: string;
  width?: number;
  height?: number;
  closable?: boolean;
  maskClosable?: boolean;
  onClose?: () => void;
  showTitle?: boolean;
  container?: HTMLElement | ShadowRoot; // 自定义容器，用于Shadow DOM
}

export class Modal {
  private overlay: HTMLElement;
  private modal: HTMLElement;
  private header: HTMLElement;
  private body: HTMLElement;
  private options: ModalOptions;
  private isVisible = false;

  constructor(options: ModalOptions) {
    this.options = { closable: true, maskClosable: true, showTitle: true, ...options };
    this.createElement();
    this.bindEvents();
  }

  /**
   * 创建模态弹窗元素
   */
  private createElement(): void {
    // 创建遮罩层
    this.overlay = document.createElement('div');
    this.overlay.className = 'modal-overlay';

    // 创建模态框
    this.modal = document.createElement('div');
    this.modal.className = 'modal';

    if (this.options.width) {
      this.modal.style.width = this.options.width + 'px';
    }
    if (this.options.height) {
      this.modal.style.height = this.options.height + 'px';
    }

    // 创建头部
    this.header = document.createElement('div');
    this.header.className = 'modal-header';

    if (this.options.showTitle && this.options.title) {
      this.header.innerHTML = `
        <h2>${this.options.title}</h2>
        ${this.options.closable ? '<button class="close-btn" type="button">&times;</button>' : ''}
      `;
    } else {
      this.header.innerHTML = `
        <div class="header-tabs" id="header-tabs"></div>
        ${this.options.closable ? '<button class="close-btn" type="button">&times;</button>' : ''}
      `;
    }

    // 创建主体
    this.body = document.createElement('div');
    this.body.className = 'modal-body';

    // 组装结构
    this.modal.appendChild(this.header);
    this.modal.appendChild(this.body);
    this.overlay.appendChild(this.modal);
  }

  /**
   * 绑定事件
   */
  private bindEvents(): void {
    // 关闭按钮事件
    if (this.options.closable) {
      const closeBtn = this.header.querySelector('.close-btn');
      closeBtn?.addEventListener('click', () => {
        this.hide();
      });
    }

    // 遮罩点击关闭
    if (this.options.maskClosable) {
      this.overlay.addEventListener('click', (e) => {
        if (e.target === this.overlay) {
          this.hide();
        }
      });
    }

    // ESC键关闭
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && this.isVisible) {
        this.hide();
      }
    });

    // 阻止模态框内的点击事件冒泡
    this.modal.addEventListener('click', (e) => {
      e.stopPropagation();
    });
  }

  /**
   * 显示模态框
   */
  show(): void {
    if (this.isVisible) return;

    // 如果指定了容器，使用容器；否则使用document.body
    const container = this.options.container || document.body;

    // 只有在overlay不在容器中时才添加
    if (!container.contains(this.overlay)) {
      container.appendChild(this.overlay);
    }

    // 强制重排以确保动画效果
    this.overlay.offsetHeight;

    // 使用requestAnimationFrame确保动画流畅
    requestAnimationFrame(() => {
      this.overlay.classList.add('show');
      this.isVisible = true;
    });

    // 只有在使用document.body时才禁用页面滚动
    if (container === document.body) {
      document.body.style.overflow = 'hidden';
    }
  }

  /**
   * 隐藏模态框
   */
  hide(): void {
    if (!this.isVisible) return;

    this.overlay.classList.remove('show');
    this.isVisible = false;

    // 等待动画完成后移除元素（与CSS动画时间匹配）
    setTimeout(() => {
      if (this.overlay.parentNode) {
        this.overlay.parentNode.removeChild(this.overlay);
      }

      // 只有在使用document.body时才恢复页面滚动
      const container = this.options.container || document.body;
      if (container === document.body) {
        document.body.style.overflow = '';
      }

      // 调用关闭回调
      if (this.options.onClose) {
        this.options.onClose();
      }
    }, 300); // 与CSS动画时间匹配
  }

  /**
   * 设置内容
   */
  setContent(content: string | HTMLElement): void {
    if (typeof content === 'string') {
      this.body.innerHTML = content;
    } else {
      this.body.innerHTML = '';
      this.body.appendChild(content);
    }
  }

  /**
   * 添加内容
   */
  appendContent(content: string | HTMLElement): void {
    if (typeof content === 'string') {
      this.body.insertAdjacentHTML('beforeend', content);
    } else {
      this.body.appendChild(content);
    }
  }

  /**
   * 设置标题
   */
  setTitle(title: string): void {
    const titleElement = this.header.querySelector('h2');
    if (titleElement) {
      titleElement.textContent = title;
    }
  }

  /**
   * 获取主体元素
   */
  getBody(): HTMLElement {
    return this.body;
  }

  /**
   * 获取模态框元素
   */
  getModal(): HTMLElement {
    return this.modal;
  }

  /**
   * 获取遮罩层元素
   */
  getOverlay(): HTMLElement {
    return this.overlay;
  }

  /**
   * 检查是否可见
   */
  isShown(): boolean {
    return this.isVisible;
  }

  /**
   * 销毁模态框
   */
  destroy(): void {
    this.hide();
    // 移除所有事件监听器会在hide()方法中的setTimeout中处理
  }
}

/**
 * 标签页组件
 */
export class TabContainer {
  private container: HTMLElement;
  private tabsContainer: HTMLElement;
  private contentContainer: HTMLElement;
  private tabs: Map<string, { button: HTMLElement; content: HTMLElement }> = new Map();
  private activeTab: string | null = null;
  private headerMode: boolean;

  constructor(headerMode: boolean = false) {
    this.headerMode = headerMode;
    this.createElement();
  }

  /**
   * 创建标签页容器
   */
  private createElement(): void {
    this.container = document.createElement('div');
    this.container.className = this.headerMode ? 'tab-container header-mode' : 'tab-container';

    if (!this.headerMode) {
      this.tabsContainer = document.createElement('div');
      this.tabsContainer.className = 'tabs';
      this.container.appendChild(this.tabsContainer);
    }

    this.contentContainer = document.createElement('div');
    this.contentContainer.className = 'tab-contents';
    this.container.appendChild(this.contentContainer);
  }

  /**
   * 设置标签页容器（用于标题栏模式）
   */
  setTabsContainer(container: HTMLElement): void {
    this.tabsContainer = container;
  }

  /**
   * 添加标签页
   */
  addTab(id: string, title: string, content: string | HTMLElement): void {
    // 创建标签按钮
    const tabButton = document.createElement('button');
    tabButton.className = this.headerMode ? 'header-tab' : 'tab';
    tabButton.textContent = title;
    tabButton.addEventListener('click', () => {
      this.setActiveTab(id);
    });

    // 创建内容容器
    const tabContent = document.createElement('div');
    tabContent.className = 'tab-content';

    if (typeof content === 'string') {
      tabContent.innerHTML = content;
    } else {
      tabContent.appendChild(content);
    }

    // 添加到容器
    if (this.tabsContainer) {
      this.tabsContainer.appendChild(tabButton);
    }
    this.contentContainer.appendChild(tabContent);

    // 保存引用
    this.tabs.set(id, { button: tabButton, content: tabContent });

    // 如果是第一个标签，设为活动状态
    if (this.tabs.size === 1) {
      this.setActiveTab(id);
    }
  }

  /**
   * 设置活动标签
   */
  setActiveTab(id: string): void {
    const tab = this.tabs.get(id);
    if (!tab) return;

    // 移除所有活动状态
    this.tabs.forEach(({ button, content }) => {
      button.classList.remove('active');
      content.classList.remove('active');
    });

    // 设置新的活动状态
    tab.button.classList.add('active');
    tab.content.classList.add('active');
    this.activeTab = id;
  }

  /**
   * 获取活动标签ID
   */
  getActiveTab(): string | null {
    return this.activeTab;
  }

  /**
   * 获取标签内容元素
   */
  getTabContent(id: string): HTMLElement | null {
    const tab = this.tabs.get(id);
    return tab ? tab.content : null;
  }

  /**
   * 获取容器元素
   */
  getContainer(): HTMLElement {
    return this.container;
  }

  /**
   * 移除标签页
   */
  removeTab(id: string): void {
    const tab = this.tabs.get(id);
    if (!tab) return;

    // 移除DOM元素
    tab.button.remove();
    tab.content.remove();

    // 从Map中删除
    this.tabs.delete(id);

    // 如果删除的是活动标签，激活第一个标签
    if (this.activeTab === id && this.tabs.size > 0) {
      const firstTabId = this.tabs.keys().next().value;
      this.setActiveTab(firstTabId);
    }
  }

  /**
   * 清空所有标签页
   */
  clear(): void {
    this.tabs.forEach((_, id) => {
      this.removeTab(id);
    });
    this.activeTab = null;
  }
}
