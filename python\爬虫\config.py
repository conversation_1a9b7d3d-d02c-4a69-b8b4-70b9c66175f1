#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LXC.WIKI 爬虫配置文件
"""

# 爬虫基本配置
CRAWLER_CONFIG = {
    'max_workers': 3,  # 最大并发线程数
    'request_delay': 2,  # 请求间隔（秒）
    'timeout': 30,  # 请求超时时间（秒）
    'retry_times': 3,  # 重试次数
    'retry_delay': 5,  # 重试间隔（秒）
    'save_history': True,  # 是否保存历史数据
    'history_file': 'lxc_wiki_history.json'
}

# 目标URL配置
TARGET_URLS = [
    {
        'url': 'https://www.lxc.wiki/cart?fid=1',
        'name': '美国',
        'take_last': 3,  # 只取最后3个分组
        'enabled': True
    },
    {
        'url': 'https://www.lxc.wiki/cart?fid=4',
        'name': '日本',
        'take_last': 3,  # 只取最后3个分组
        'enabled': True
    },
    {
        'url': 'https://www.lxc.wiki/cart?fid=3&gid=58',
        'name': '新加坡',
        'take_last': 3,  # 只取最后3个分组
        'enabled': True
    }
]

# 通知配置
NOTIFICATION_CONFIG = {
    'enable_notifications': True,  # 是否启用通知
    'notify_on_stock_change': True,  # 库存变化时是否通知
    'notify_on_available_stock': True,  # 发现有库存时是否通知
    'notify_summary': True,  # 是否发送汇总通知
    'max_products_in_summary': 5,  # 汇总通知中最多显示的产品数量
}

# 通知服务配置（根据需要配置相应的服务）
NOTIFY_SERVICES = {
    # 控制台输出
    'CONSOLE': True,

    # Server酱（微信推送）
    'PUSH_KEY': '',  # 填入你的Server酱PUSH_KEY

    # 钉钉机器人
    'DD_BOT_TOKEN': '4d7fdcf6dd40503211184fc5c97a3c0f5d4af7c9f0e76d7a1d39a98634a91da9',  # 钉钉机器人Token
    'DD_BOT_SECRET': 'SEC7ffc1450a973f5a41864659b361c7017174aecd68f6772195cfdc9eb76d62b53',  # 钉钉机器人Secret

    # 企业微信机器人
    'QYWX_KEY': '',  # 企业微信机器人Key

    # Telegram Bot
    'TG_BOT_TOKEN': '',  # Telegram Bot Token
    'TG_USER_ID': '',   # Telegram User ID

    # Bark (iOS推送)
    'BARK_PUSH': '',  # Bark推送地址或设备码

    # PushPlus
    'PUSH_PLUS_TOKEN': '',  # PushPlus Token

    # 邮件通知
    'SMTP_SERVER': '',     # SMTP服务器，如: smtp.qq.com:587
    'SMTP_EMAIL': '',      # 发送邮箱
    'SMTP_PASSWORD': '',   # 邮箱密码或授权码
    'SMTP_NAME': '',       # 发送者姓名
    'SMTP_SSL': 'true',    # 是否使用SSL
}

# 监控配置
MONITOR_CONFIG = {
    'check_interval': 300,  # 检查间隔（秒），5分钟
    'enable_continuous_monitor': False,  # 是否启用持续监控
    'monitor_keywords': [],  # 监控关键词，如果产品名包含这些词则优先通知
    'price_threshold': 0,   # 价格阈值，低于此价格的产品会优先通知
}

# 过滤配置
FILTER_CONFIG = {
    'exclude_groups': [],   # 排除的分组名称
    'include_groups': [],   # 只包含的分组名称（为空则包含所有）
    'exclude_keywords': ['测试', 'test'],  # 排除包含这些关键词的产品
    'min_price': 0,         # 最低价格过滤
    'max_price': 999999,    # 最高价格过滤
}

def get_config():
    """获取完整配置"""
    return {
        'crawler': CRAWLER_CONFIG,
        'target_urls': TARGET_URLS,
        'notification': NOTIFICATION_CONFIG,
        'notify_services': NOTIFY_SERVICES,
        'monitor': MONITOR_CONFIG,
        'filter': FILTER_CONFIG
    }

def update_config_from_env():
    """从环境变量更新配置"""
    import os

    # 更新通知服务配置
    for key in NOTIFY_SERVICES:
        env_value = os.getenv(key)
        if env_value:
            NOTIFY_SERVICES[key] = env_value

    # 更新其他配置
    if os.getenv('MAX_WORKERS'):
        CRAWLER_CONFIG['max_workers'] = int(os.getenv('MAX_WORKERS'))

    if os.getenv('REQUEST_DELAY'):
        CRAWLER_CONFIG['request_delay'] = float(os.getenv('REQUEST_DELAY'))

    if os.getenv('ENABLE_NOTIFICATIONS'):
        NOTIFICATION_CONFIG['enable_notifications'] = os.getenv('ENABLE_NOTIFICATIONS').lower() == 'true'

if __name__ == '__main__':
    # 测试配置
    import json
    config = get_config()
    print(json.dumps(config, indent=2, ensure_ascii=False))
