// 账号切换面板组件

import { AccountStorage } from '../utils/storage';

export interface AccountPanelState {
  account: string;
  password: string;
  token: string;
  loading: boolean;
}

export interface AccountPanelEvents {
  onAccountLogin: (account: string) => Promise<void>;
  onTokenLogin: (token: string) => void;
  onClearData: () => void;
  onShowMessage: (message: string, type: 'success' | 'error' | 'warning') => void;
}

export class AccountPanel {
  private container: HTMLElement;
  private state: AccountPanelState;
  private events: AccountPanelEvents;

  constructor(state: AccountPanelState, events: AccountPanelEvents) {
    this.state = state;
    this.events = events;
    this.container = this.createElement();
    this.bindEvents();
  }

  /**
   * 创建账号面板元素
   */
  private createElement(): HTMLElement {
    const panel = document.createElement('div');
    panel.className = 'account-panel';
    panel.innerHTML = this.getTemplate();
    return panel;
  }

  /**
   * 获取HTML模板
   */
  private getTemplate(): string {
    return `
      <form class="account-form">
        <div class="form-section">
          <div class="form-row">
            <label class="form-label">账号:</label>
            <input type="text" class="form-input" id="account-input" placeholder="请输入账号" value="${this.state.account}">
          </div>
          <div class="form-actions">
            <button type="button" class="action-btn primary" id="default-account-btn">
              <span class="btn-icon">👤</span>
              <span>默认账号</span>
            </button>
            <button type="submit" class="action-btn primary" id="login-btn">
              <span class="btn-icon">🔑</span>
              <span class="btn-text">账号登录</span>
              <div class="loading" style="display: none;"></div>
            </button>
          </div>
        </div>

        <div class="form-section">
          <div class="form-row">
            <label class="form-label">Token:</label>
            <input type="text" class="form-input" id="token-input" placeholder="直接输入Token登录" value="${this.state.token}">
          </div>
          <div class="form-actions">
            <button type="button" class="action-btn secondary" id="token-login-btn">
              <span class="btn-icon">🎫</span>
              <span>使用Token登录</span>
            </button>
          </div>
        </div>

        <div class="form-section">
          <div class="token-display">
            <div class="token-label">当前Token:</div>
            <div class="token-value" id="current-token">${AccountStorage.getToken() || '暂无token'}</div>
          </div>
          <div class="form-actions">
            <button type="button" class="action-btn danger" id="clear-data-btn">
              <span class="btn-icon">🗑️</span>
              <span>清空数据</span>
            </button>
          </div>
        </div>
      </form>
    `;
  }

  /**
   * 绑定事件
   */
  private bindEvents(): void {
    const accountInput = this.container.querySelector('#account-input') as HTMLInputElement;
    const tokenInput = this.container.querySelector('#token-input') as HTMLInputElement;
    const defaultAccountBtn = this.container.querySelector('#default-account-btn') as HTMLButtonElement;
    const tokenLoginBtn = this.container.querySelector('#token-login-btn') as HTMLButtonElement;
    const currentTokenEl = this.container.querySelector('#current-token') as HTMLElement;
    const clearDataBtn = this.container.querySelector('#clear-data-btn') as HTMLButtonElement;
    const loginForm = this.container.querySelector('.account-form') as HTMLFormElement;

    // 检查元素是否存在
    if (!accountInput || !tokenInput || !defaultAccountBtn || !tokenLoginBtn || !currentTokenEl || !clearDataBtn || !loginForm) {
      return;
    }

    // 账号输入变化
    accountInput.addEventListener('input', (e) => {
      this.state.account = (e.target as HTMLInputElement).value;
    });

    // Token输入变化
    tokenInput.addEventListener('input', (e) => {
      this.state.token = (e.target as HTMLInputElement).value;
    });

    // 默认账号按钮
    defaultAccountBtn.addEventListener('click', () => {
      this.state.account = '***********';
      accountInput.value = this.state.account;
      this.handleAccountLogin();
    });

    // Token登录按钮
    tokenLoginBtn.addEventListener('click', () => {
      this.handleTokenLogin();
    });

    // 当前Token点击复制
    currentTokenEl.addEventListener('click', () => {
      this.copyToClipboard(AccountStorage.getToken() || '');
    });

    // 清空数据按钮
    clearDataBtn.addEventListener('click', () => {
      if (confirm('确定要清空所有数据吗？')) {
        this.events.onClearData();
        this.updateCurrentToken();
        this.events.onShowMessage('数据已清空', 'success');
      }
    });

    // 表单提交
    loginForm.addEventListener('submit', (e) => {
      e.preventDefault();
      this.handleAccountLogin();
    });
  }

  /**
   * 处理账号登录
   */
  private async handleAccountLogin(): Promise<void> {
    if (!this.state.account.trim()) {
      this.events.onShowMessage('请输入账号', 'error');
      return;
    }

    try {
      await this.events.onAccountLogin(this.state.account);
    } catch (error) {
      this.events.onShowMessage('账号登录失败', 'error');
    }
  }

  /**
   * 处理Token登录
   */
  private handleTokenLogin(): void {
    if (!this.state.token.trim()) {
      this.events.onShowMessage('请输入Token', 'error');
      return;
    }

    this.events.onTokenLogin(this.state.token);
  }

  /**
   * 复制到剪贴板
   */
  private async copyToClipboard(text: string): Promise<void> {
    try {
      await navigator.clipboard.writeText(text);
      this.events.onShowMessage('已复制到剪贴板', 'success');
    } catch (error) {
      // 降级方案
      const textArea = document.createElement('textarea');
      textArea.value = text;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      this.events.onShowMessage('已复制到剪贴板', 'success');
    }
  }

  /**
   * 设置加载状态
   */
  setLoading(loading: boolean): void {
    this.state.loading = loading;

    const loginBtn = this.container.querySelector('#login-btn') as HTMLButtonElement;
    const btnText = loginBtn.querySelector('.btn-text') as HTMLElement;
    const loadingEl = loginBtn.querySelector('.loading') as HTMLElement;

    if (loading) {
      btnText.style.display = 'none';
      loadingEl.style.display = 'inline-block';
      loginBtn.disabled = true;
    } else {
      btnText.style.display = 'inline';
      loadingEl.style.display = 'none';
      loginBtn.disabled = false;
    }
  }

  /**
   * 更新当前Token显示
   */
  updateCurrentToken(): void {
    const currentTokenEl = this.container.querySelector('#current-token') as HTMLElement;
    currentTokenEl.textContent = AccountStorage.getToken() || '暂无token';
  }

  /**
   * 更新状态
   */
  updateState(newState: Partial<AccountPanelState>): void {
    Object.assign(this.state, newState);

    // 更新输入框值
    const accountInput = this.container.querySelector('#account-input') as HTMLInputElement;
    const tokenInput = this.container.querySelector('#token-input') as HTMLInputElement;

    if (newState.account !== undefined) {
      accountInput.value = newState.account;
    }
    if (newState.token !== undefined) {
      tokenInput.value = newState.token;
    }
  }

  /**
   * 获取容器元素
   */
  getContainer(): HTMLElement {
    return this.container;
  }

  /**
   * 销毁组件
   */
  destroy(): void {
    if (this.container.parentNode) {
      this.container.parentNode.removeChild(this.container);
    }
  }
}
