// ==UserScript==
// @name       account-change
// @namespace  npm/vite-plugin-monkey
// @version    0.0.0
// <AUTHOR>
// @icon       https://vitejs.dev/logo.svg
// @match      http://localhost:9100/*
// @match      https://cq24.91cloudpay.com:4443/*
// @match      http://portal.local:8080/*
// @match      https://kkl-dev.91cloudpay.com:4443/*
// @match      https://kkl.91cloudpay.com:4443/*
// @grant      GM_addStyle
// ==/UserScript==

(function () {
    'use strict';

    var T=Object.defineProperty;var C=(l,e,t)=>e in l?T(l,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):l[e]=t;var n=(l,e,t)=>C(l,typeof e!="symbol"?e+"":e,t);class S{constructor(e){n(this,"element");n(this,"isDragging",!1);n(this,"dragOffset",{x:0,y:0});n(this,"onClick");this.onClick=e,this.createElement(),this.bindEvents();}createElement(){this.element=document.createElement("button"),this.element.className="floating-button",this.element.innerHTML=`
      <svg class="icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" fill="currentColor"/>
      </svg>
    `;const t=20*.75;this.setPosition(window.innerHeight/2,-t);}bindEvents(){this.element.addEventListener("click",()=>{this.isDragging||this.onClick();}),this.element.addEventListener("mousedown",this.handleMouseDown.bind(this)),document.addEventListener("mousemove",this.handleMouseMove.bind(this)),document.addEventListener("mouseup",this.handleMouseUp.bind(this)),this.element.addEventListener("touchstart",this.handleTouchStart.bind(this),{passive:!1}),document.addEventListener("touchmove",this.handleTouchMove.bind(this),{passive:!1}),document.addEventListener("touchend",this.handleTouchEnd.bind(this)),window.addEventListener("resize",this.handleResize.bind(this));}handleMouseDown(e){e.preventDefault(),this.startDrag(e.clientX,e.clientY);}handleMouseMove(e){this.isDragging&&(e.preventDefault(),this.drag(e.clientX,e.clientY));}handleMouseUp(){this.endDrag();}handleTouchStart(e){e.preventDefault();const t=e.touches[0];this.startDrag(t.clientX,t.clientY);}handleTouchMove(e){if(this.isDragging){e.preventDefault();const t=e.touches[0];this.drag(t.clientX,t.clientY);}}handleTouchEnd(){this.endDrag();}startDrag(e,t){this.isDragging=!0,this.element.classList.add("dragging");const r=this.element.getBoundingClientRect();this.dragOffset={x:e-r.left,y:t-r.top};}drag(e,t){const r=e-this.dragOffset.x,s=t-this.dragOffset.y,a=window.innerWidth-this.element.offsetWidth,o=window.innerHeight-this.element.offsetHeight,u=Math.max(0,Math.min(r,a)),c=Math.max(0,Math.min(s,o));this.setPosition(c,u);}endDrag(){this.isDragging&&(this.isDragging=!1,this.element.classList.remove("dragging"),this.snapToEdge());}snapToEdge(){const e=this.element.getBoundingClientRect(),t=e.left+e.width/2,r=window.innerWidth,s=e.width*.75,a=t<r/2?-s:r-e.width+s;this.element.style.transition="left 0.3s cubic-bezier(0.4, 0, 0.2, 1)",this.element.style.left=a+"px",setTimeout(()=>{this.element.style.transition="";},300);}setPosition(e,t){this.element.style.top=e+"px",this.element.style.left=t+"px";}handleResize(){const e=this.element.getBoundingClientRect(),t=window.innerWidth-e.width,r=window.innerHeight-e.height;e.left>t&&(this.element.style.left=t+"px"),e.top>r&&(this.element.style.top=r+"px");}show(){this.element.style.display="flex";}hide(){this.element.style.display="none";}setLoading(e){const t=this.element;e?(t.innerHTML=`
        <div class="loading"></div>
      `,t.disabled=!0):(t.innerHTML=`
        <svg class="icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" fill="currentColor"/>
        </svg>
      `,t.disabled=!1);}getElement(){return this.element}destroy(){document.removeEventListener("mousemove",this.handleMouseMove.bind(this)),document.removeEventListener("mouseup",this.handleMouseUp.bind(this)),document.removeEventListener("touchmove",this.handleTouchMove.bind(this)),document.removeEventListener("touchend",this.handleTouchEnd.bind(this)),window.removeEventListener("resize",this.handleResize.bind(this)),this.element.parentNode&&this.element.parentNode.removeChild(this.element);}}class k{constructor(e){n(this,"overlay");n(this,"modal");n(this,"header");n(this,"body");n(this,"options");n(this,"isVisible",!1);this.options={closable:!0,maskClosable:!0,showTitle:!0,...e},this.createElement(),this.bindEvents();}createElement(){this.overlay=document.createElement("div"),this.overlay.className="modal-overlay",this.modal=document.createElement("div"),this.modal.className="modal",this.options.width&&(this.modal.style.width=this.options.width+"px"),this.options.height&&(this.modal.style.height=this.options.height+"px"),this.header=document.createElement("div"),this.header.className="modal-header",this.options.showTitle&&this.options.title?this.header.innerHTML=`
        <h2>${this.options.title}</h2>
        ${this.options.closable?'<button class="close-btn" type="button">&times;</button>':""}
      `:this.header.innerHTML=`
        <div class="header-tabs" id="header-tabs"></div>
        ${this.options.closable?'<button class="close-btn" type="button">&times;</button>':""}
      `,this.body=document.createElement("div"),this.body.className="modal-body",this.modal.appendChild(this.header),this.modal.appendChild(this.body),this.overlay.appendChild(this.modal);}bindEvents(){if(this.options.closable){const e=this.header.querySelector(".close-btn");e==null||e.addEventListener("click",()=>{this.hide();});}this.options.maskClosable&&this.overlay.addEventListener("click",e=>{e.target===this.overlay&&this.hide();}),document.addEventListener("keydown",e=>{e.key==="Escape"&&this.isVisible&&this.hide();}),this.modal.addEventListener("click",e=>{e.stopPropagation();});}show(){if(this.isVisible)return;const e=this.options.container||document.body;e.contains(this.overlay)||e.appendChild(this.overlay),this.overlay.offsetHeight,requestAnimationFrame(()=>{this.overlay.classList.add("show"),this.isVisible=!0;}),e===document.body&&(document.body.style.overflow="hidden");}hide(){this.isVisible&&(this.overlay.classList.remove("show"),this.isVisible=!1,setTimeout(()=>{this.overlay.parentNode&&this.overlay.parentNode.removeChild(this.overlay),(this.options.container||document.body)===document.body&&(document.body.style.overflow=""),this.options.onClose&&this.options.onClose();},300));}setContent(e){typeof e=="string"?this.body.innerHTML=e:(this.body.innerHTML="",this.body.appendChild(e));}appendContent(e){typeof e=="string"?this.body.insertAdjacentHTML("beforeend",e):this.body.appendChild(e);}setTitle(e){const t=this.header.querySelector("h2");t&&(t.textContent=e);}getBody(){return this.body}getModal(){return this.modal}getOverlay(){return this.overlay}isShown(){return this.isVisible}destroy(){this.hide();}}class q{constructor(e=!1){n(this,"container");n(this,"tabsContainer");n(this,"contentContainer");n(this,"tabs",new Map);n(this,"activeTab",null);n(this,"headerMode");this.headerMode=e,this.createElement();}createElement(){this.container=document.createElement("div"),this.container.className=this.headerMode?"tab-container header-mode":"tab-container",this.headerMode||(this.tabsContainer=document.createElement("div"),this.tabsContainer.className="tabs",this.container.appendChild(this.tabsContainer)),this.contentContainer=document.createElement("div"),this.contentContainer.className="tab-contents",this.container.appendChild(this.contentContainer);}setTabsContainer(e){this.tabsContainer=e;}addTab(e,t,r){const s=document.createElement("button");s.className=this.headerMode?"header-tab":"tab",s.textContent=t,s.addEventListener("click",()=>{this.setActiveTab(e);});const a=document.createElement("div");a.className="tab-content",typeof r=="string"?a.innerHTML=r:a.appendChild(r),this.tabsContainer&&this.tabsContainer.appendChild(s),this.contentContainer.appendChild(a),this.tabs.set(e,{button:s,content:a}),this.tabs.size===1&&this.setActiveTab(e);}setActiveTab(e){const t=this.tabs.get(e);t&&(this.tabs.forEach(({button:r,content:s})=>{r.classList.remove("active"),s.classList.remove("active");}),t.button.classList.add("active"),t.content.classList.add("active"),this.activeTab=e);}getActiveTab(){return this.activeTab}getTabContent(e){const t=this.tabs.get(e);return t?t.content:null}getContainer(){return this.container}removeTab(e){const t=this.tabs.get(e);if(t&&(t.button.remove(),t.content.remove(),this.tabs.delete(e),this.activeTab===e&&this.tabs.size>0)){const r=this.tabs.keys().next().value;this.setActiveTab(r);}}clear(){this.tabs.forEach((e,t)=>{this.removeTab(t);}),this.activeTab=null;}}class p{static setItem(e,t,r={}){try{const s=this.STORAGE_PREFIX+e,a={value:t,timestamp:Date.now(),expiry:r.expiry?Date.now()+r.expiry:null};let o=JSON.stringify(a);r.encrypt&&(o=this.encrypt(o)),localStorage.setItem(s,o);}catch(s){console.error("存储数据失败:",s);}}static getItem(e,t=null,r=!1){try{const s=this.STORAGE_PREFIX+e;let a=localStorage.getItem(s);if(!a||r&&(a=this.decrypt(a),!a))return t;const o=JSON.parse(a);return o.expiry&&Date.now()>o.expiry?(this.removeItem(e),t):o.value}catch(s){return console.error("读取数据失败:",s),t}}static removeItem(e){try{const t=this.STORAGE_PREFIX+e;localStorage.removeItem(t);}catch(t){console.error("删除数据失败:",t);}}static clear(){try{Object.keys(localStorage).forEach(t=>{t.startsWith(this.STORAGE_PREFIX)&&localStorage.removeItem(t);});}catch(e){console.error("清空数据失败:",e);}}static getAllItems(){const e={};try{Object.keys(localStorage).forEach(r=>{if(r.startsWith(this.STORAGE_PREFIX)){const s=r.replace(this.STORAGE_PREFIX,"");e[s]=this.getItem(s);}});}catch(t){console.error("获取所有数据失败:",t);}return e}static hasItem(e){const t=this.STORAGE_PREFIX+e;return localStorage.getItem(t)!==null}static getStorageSize(){let e=0;try{Object.keys(localStorage).forEach(r=>{if(r.startsWith(this.STORAGE_PREFIX)){const s=localStorage.getItem(r);s&&(e+=r.length+s.length);}});}catch(t){console.error("计算存储大小失败:",t);}return e}static encrypt(e){let t="";for(let r=0;r<e.length;r++){const s=e.charCodeAt(r)^this.ENCRYPTION_KEY.charCodeAt(r%this.ENCRYPTION_KEY.length);t+=String.fromCharCode(s);}return btoa(t)}static decrypt(e){try{const t=atob(e);let r="";for(let s=0;s<t.length;s++){const a=t.charCodeAt(s)^this.ENCRYPTION_KEY.charCodeAt(s%this.ENCRYPTION_KEY.length);r+=String.fromCharCode(a);}return r}catch{return ""}}}n(p,"STORAGE_PREFIX","account_change_"),n(p,"ENCRYPTION_KEY","ac_key_2024");class y{static saveToken(e){p.setItem("token",e,{encrypt:!0}),localStorage.setItem("token",e);}static getToken(){const e=p.getItem("token",null,!0);return e||localStorage.getItem("token")}static saveAccount(e){p.setItem("last_account",e);}static getLastAccount(){return p.getItem("last_account")}static saveCorpId(e){p.setItem("corp_id",e),localStorage.setItem("corpId",e);}static getCorpId(){const e=p.getItem("corp_id");return e||localStorage.getItem("corpId")}static saveSettings(e){p.setItem("user_settings",e);}static getSettings(){return p.getItem("user_settings",{})}static clearAll(){p.clear(),localStorage.removeItem("token"),localStorage.removeItem("corpId");}}class L{constructor(e,t){n(this,"container");n(this,"state");n(this,"events");this.state=e,this.events=t,this.container=this.createElement(),this.bindEvents();}createElement(){const e=document.createElement("div");return e.className="account-panel",e.innerHTML=this.getTemplate(),e}getTemplate(){return `
      <form class="account-form">
        <div class="form-section">
          <div class="form-row">
            <label class="form-label">账号:</label>
            <input type="text" class="form-input" id="account-input" placeholder="请输入账号" value="${this.state.account}">
          </div>
          <div class="form-actions">
            <button type="button" class="action-btn primary" id="default-account-btn">
              <span class="btn-icon">👤</span>
              <span>默认账号</span>
            </button>
            <button type="submit" class="action-btn primary" id="login-btn">
              <span class="btn-icon">🔑</span>
              <span class="btn-text">账号登录</span>
              <div class="loading" style="display: none;"></div>
            </button>
          </div>
        </div>

        <div class="form-section">
          <div class="form-row">
            <label class="form-label">Token:</label>
            <input type="text" class="form-input" id="token-input" placeholder="直接输入Token登录" value="${this.state.token}">
          </div>
          <div class="form-actions">
            <button type="button" class="action-btn secondary" id="token-login-btn">
              <span class="btn-icon">🎫</span>
              <span>使用Token登录</span>
            </button>
          </div>
        </div>

        <div class="form-section">
          <div class="token-display">
            <div class="token-label">当前Token:</div>
            <div class="token-value" id="current-token">${y.getToken()||"暂无token"}</div>
          </div>
          <div class="form-actions">
            <button type="button" class="action-btn danger" id="clear-data-btn">
              <span class="btn-icon">🗑️</span>
              <span>清空数据</span>
            </button>
          </div>
        </div>
      </form>
    `}bindEvents(){const e=this.container.querySelector("#account-input"),t=this.container.querySelector("#token-input"),r=this.container.querySelector("#default-account-btn"),s=this.container.querySelector("#token-login-btn"),a=this.container.querySelector("#current-token"),o=this.container.querySelector("#clear-data-btn"),u=this.container.querySelector(".account-form");!e||!t||!r||!s||!a||!o||!u||(e.addEventListener("input",c=>{this.state.account=c.target.value;}),t.addEventListener("input",c=>{this.state.token=c.target.value;}),r.addEventListener("click",()=>{this.state.account="***********",e.value=this.state.account,this.handleAccountLogin();}),s.addEventListener("click",()=>{this.handleTokenLogin();}),a.addEventListener("click",()=>{this.copyToClipboard(y.getToken()||"");}),o.addEventListener("click",()=>{confirm("确定要清空所有数据吗？")&&(this.events.onClearData(),this.updateCurrentToken(),this.events.onShowMessage("数据已清空","success"));}),u.addEventListener("submit",c=>{c.preventDefault(),this.handleAccountLogin();}));}async handleAccountLogin(){if(!this.state.account.trim()){this.events.onShowMessage("请输入账号","error");return}try{await this.events.onAccountLogin(this.state.account);}catch{this.events.onShowMessage("账号登录失败","error");}}handleTokenLogin(){if(!this.state.token.trim()){this.events.onShowMessage("请输入Token","error");return}this.events.onTokenLogin(this.state.token);}async copyToClipboard(e){try{await navigator.clipboard.writeText(e),this.events.onShowMessage("已复制到剪贴板","success");}catch{const r=document.createElement("textarea");r.value=e,document.body.appendChild(r),r.select(),document.execCommand("copy"),document.body.removeChild(r),this.events.onShowMessage("已复制到剪贴板","success");}}setLoading(e){this.state.loading=e;const t=this.container.querySelector("#login-btn"),r=t.querySelector(".btn-text"),s=t.querySelector(".loading");e?(r.style.display="none",s.style.display="inline-block",t.disabled=!0):(r.style.display="inline",s.style.display="none",t.disabled=!1);}updateCurrentToken(){const e=this.container.querySelector("#current-token");e.textContent=y.getToken()||"暂无token";}updateState(e){Object.assign(this.state,e);const t=this.container.querySelector("#account-input"),r=this.container.querySelector("#token-input");e.account!==void 0&&(t.value=e.account),e.token!==void 0&&(r.value=e.token);}getContainer(){return this.container}destroy(){this.container.parentNode&&this.container.parentNode.removeChild(this.container);}}const g=class g{constructor(){n(this,"requests",new Map);n(this,"listeners",new Set);n(this,"originalXHR");n(this,"originalFetch");n(this,"isIntercepting",!1);this.originalXHR=window.XMLHttpRequest,this.originalFetch=window.fetch;}static getInstance(){return g.instance||(g.instance=new g),g.instance}start(){this.isIntercepting||(this.interceptXHR(),this.interceptFetch(),this.isIntercepting=!0,console.log("请求拦截器已启动"));}stop(){this.isIntercepting&&(window.XMLHttpRequest=this.originalXHR,window.fetch=this.originalFetch,this.isIntercepting=!1,console.log("请求拦截器已停止"));}addListener(e){this.listeners.add(e);}removeListener(e){this.listeners.delete(e);}getRequests(){return Array.from(this.requests.values()).sort((e,t)=>t.timestamp-e.timestamp)}clearRequests(){this.requests.clear(),this.notifyListeners();}getStats(){const e=this.getRequests(),t=e.length,r=e.filter(o=>o.status&&o.status>=200&&o.status<300).length,s=e.filter(o=>o.status&&o.status>=400).length,a=e.filter(o=>!o.status).length;return {total:t,success:r,error:s,pending:a}}interceptXHR(){const e=this;window.XMLHttpRequest=function(){const t=new e.originalXHR,r=e.generateId();let s;const a=t.open;t.open=function(c,i,...h){return s={id:r,method:c.toUpperCase(),url:i,headers:{},timestamp:Date.now()},a.apply(this,[c,i,...h])};const o=t.setRequestHeader;t.setRequestHeader=function(c,i){return s&&(s.headers[c]=i),o.apply(this,[c,i])};const u=t.send;return t.send=function(c){return s&&(s.body=c,e.requests.set(r,s),console.log("XHR请求开始:",s),e.notifyListeners()),t.addEventListener("readystatechange",function(){var i;if(t.readyState===4&&s){s.status=t.status,s.statusText=t.statusText,s.duration=Date.now()-s.timestamp;const h={},d=t.getAllResponseHeaders();d&&d.split(`\r
`).forEach(m=>{const[f,b]=m.split(": ");f&&b&&(h[f]=b);}),s.responseHeaders=h;try{s.responseBody=t.responseText,s.size=((i=t.responseText)==null?void 0:i.length)||0;}catch{s.responseBody="[无法读取响应体]";}e.requests.set(r,s),e.notifyListeners();}}),t.addEventListener("error",function(){s&&(s.error="网络错误",s.duration=Date.now()-s.timestamp,e.requests.set(r,s),e.notifyListeners());}),u.apply(this,[c])},t};}interceptFetch(){const e=this;window.fetch=async function(t,r){var h;const s=e.generateId(),a=Date.now(),o=typeof t=="string"?t:t.toString(),u=((h=r==null?void 0:r.method)==null?void 0:h.toUpperCase())||"GET",c={};r!=null&&r.headers&&(r.headers instanceof Headers?r.headers.forEach((d,m)=>{c[m]=d;}):Array.isArray(r.headers)?r.headers.forEach(([d,m])=>{c[d]=m;}):Object.entries(r.headers).forEach(([d,m])=>{c[d]=m;}));const i={id:s,method:u,url:o,headers:c,body:r==null?void 0:r.body,timestamp:a};e.requests.set(s,i),console.log("Fetch请求开始:",i),e.notifyListeners();try{const d=await e.originalFetch.apply(window,[t,r]);i.status=d.status,i.statusText=d.statusText,i.duration=Date.now()-a;const m={};d.headers.forEach((b,E)=>{m[E]=b;}),i.responseHeaders=m;const f=d.clone();try{const b=await f.text();i.responseBody=b,i.size=b.length;}catch{i.responseBody="[无法读取响应体]";}return e.requests.set(s,i),e.notifyListeners(),d}catch(d){throw i.error=d instanceof Error?d.message:"请求失败",i.duration=Date.now()-a,e.requests.set(s,i),e.notifyListeners(),d}};}generateId(){return Date.now().toString(36)+Math.random().toString(36).substring(2)}notifyListeners(){this.listeners.forEach(e=>{try{const t=this.getRequests(),r=t.length>0?t[0]:null;e(r);}catch(t){console.error("请求监听器执行失败:",t);}});}};n(g,"instance");let x=g;const v=x.getInstance();class M{constructor(e){n(this,"container");n(this,"events");this.events=e,this.container=this.createElement(),this.bindEvents(),this.setupRequestListener(),this.updateRequestList();}createElement(){const e=document.createElement("div");return e.className="request-monitor",e.innerHTML=this.getTemplate(),e}getTemplate(){return `
      <div class="monitor-header">
        <div class="monitor-stats">
          <div class="stat-item">
            <span class="stat-dot success"></span>
            <span id="success-count">0</span> 成功
          </div>
          <div class="stat-item">
            <span class="stat-dot error"></span>
            <span id="error-count">0</span> 失败
          </div>
          <div class="stat-item">
            <span class="stat-dot pending"></span>
            <span id="pending-count">0</span> 进行中
          </div>
        </div>
        <div class="monitor-controls">
          <button class="btn btn-sm btn-secondary" id="clear-requests-btn">清空</button>
          <button class="btn btn-sm btn-secondary" id="export-requests-btn">导出</button>
        </div>
      </div>

      <div class="request-filters">
        <input type="text" class="filter-input" id="filter-url" placeholder="过滤URL...">
        <select class="filter-select" id="filter-method">
          <option value="">所有方法</option>
          <option value="GET">GET</option>
          <option value="POST">POST</option>
          <option value="PUT">PUT</option>
          <option value="DELETE">DELETE</option>
          <option value="PATCH">PATCH</option>
        </select>
        <select class="filter-select" id="filter-status">
          <option value="">所有状态</option>
          <option value="success">成功</option>
          <option value="error">失败</option>
          <option value="pending">进行中</option>
        </select>
      </div>

      <div class="request-list" id="request-list">
        <div class="empty-state">
          <div class="empty-icon">📡</div>
          <div class="empty-text">暂无请求记录</div>
        </div>
      </div>
    `}bindEvents(){const e=this.container.querySelector("#clear-requests-btn"),t=this.container.querySelector("#export-requests-btn"),r=this.container.querySelector("#filter-url"),s=this.container.querySelector("#filter-method"),a=this.container.querySelector("#filter-status");e.addEventListener("click",()=>{v.clearRequests(),this.updateRequestList();}),t.addEventListener("click",()=>{this.exportRequests();}),[r,s,a].forEach(o=>{o.addEventListener("input",()=>{this.updateRequestList();});});}setupRequestListener(){v.addListener(()=>{setTimeout(()=>{this.updateRequestList();},0);});}updateRequestList(){const e=this.getFilteredRequests(),t=v.getStats(),r=this.container.querySelector("#request-list");if(r){if(this.updateRequestStats(t),e.length===0){r.innerHTML=`
        <div class="empty-state">
          <div class="empty-icon">📡</div>
          <div class="empty-text">暂无请求记录</div>
        </div>
      `;return}r.innerHTML=e.map(s=>this.createRequestItem(s)).join(""),r.querySelectorAll(".request-item").forEach((s,a)=>{s.addEventListener("click",()=>{this.showRequestDetail(e[a]);});});}}getFilteredRequests(){var o,u,c,i;const e=v.getRequests(),t=((u=(o=this.container.querySelector("#filter-url"))==null?void 0:o.value)==null?void 0:u.toLowerCase())||"",r=((c=this.container.querySelector("#filter-method"))==null?void 0:c.value)||"",s=((i=this.container.querySelector("#filter-status"))==null?void 0:i.value)||"",a=e.filter(h=>!(t&&!h.url.toLowerCase().includes(t)||r&&h.method!==r||s&&this.getRequestStatus(h)!==s));return console.log("过滤后请求数量:",a.length),a}getRequestStatus(e){return e.error?"error":e.status?e.status>=200&&e.status<300?"success":e.status>=400?"error":"success":"pending"}updateRequestStats(e){const t=this.container.querySelector("#success-count"),r=this.container.querySelector("#error-count"),s=this.container.querySelector("#pending-count");t.textContent=e.success.toString(),r.textContent=e.error.toString(),s.textContent=e.pending.toString();}createRequestItem(e){const t=this.getRequestStatus(e),r=t==="success"?"success":t==="error"?"error":"pending",s=e.status?e.status.toString():"进行中",a=e.duration?`${e.duration}ms`:"-",o=e.size?this.formatBytes(e.size):"-",u=new Date(e.timestamp).toLocaleTimeString();return `
      <div class="request-item">
        <div class="request-header">
          <span class="request-method ${e.method}">${e.method}</span>
          <div class="request-status">
            <span class="status-code ${r}">${s}</span>
            <span class="request-time">${u}</span>
          </div>
        </div>
        <div class="request-url">${e.url}</div>
        <div class="request-meta">
          <span class="request-size">${o}</span>
          <span class="request-duration">${a}</span>
        </div>
      </div>
    `}formatBytes(e){if(e===0)return "0 B";const t=1024,r=["B","KB","MB","GB"],s=Math.floor(Math.log(e)/Math.log(t));return parseFloat((e/Math.pow(t,s)).toFixed(2))+" "+r[s]}showRequestDetail(e){const t=new k({title:`请求详情 - ${e.method}`,width:700,height:600,showTitle:!0}),r=new q;t.getModal().classList.add("request-detail-modal");const a=this.createGeneralInfoTab(e);if(r.addTab("general","基本信息",a),Object.keys(e.headers).length>0){const o=this.createHeadersTab(e.headers);r.addTab("request-headers","请求头",o);}if(e.responseHeaders&&Object.keys(e.responseHeaders).length>0){const o=this.createHeadersTab(e.responseHeaders);r.addTab("response-headers","响应头",o);}if(e.body){const o=this.createBodyTab(e.body);r.addTab("request-body","请求体",o);}if(e.responseBody){const o=this.createBodyTab(e.responseBody);r.addTab("response-body","响应体",o);}t.setContent(r.getContainer()),t.show();}createGeneralInfoTab(e){const t=document.createElement("div");t.className="detail-content";const r=[["URL",e.url],["方法",e.method],["状态码",e.status?e.status.toString():"进行中"],["状态文本",e.statusText||"-"],["请求时间",new Date(e.timestamp).toLocaleString()],["响应时间",e.duration?`${e.duration}ms`:"-"],["响应大小",e.size?this.formatBytes(e.size):"-"],["错误信息",e.error||"-"]];return t.innerHTML=`
      <div class="key-value-list">
        ${r.map(([s,a])=>`
          <div class="key-value-item">
            <div class="key">${s}:</div>
            <div class="value">${a}</div>
          </div>
        `).join("")}
      </div>
    `,t}createHeadersTab(e){const t=document.createElement("div");return t.className="detail-content",t.innerHTML=`
      <div class="key-value-list">
        ${Object.entries(e).map(([r,s])=>`
          <div class="key-value-item">
            <div class="key">${r}:</div>
            <div class="value">${s}</div>
          </div>
        `).join("")}
      </div>
    `,t}createBodyTab(e){const t=document.createElement("div");t.className="detail-content";let r="";if(typeof e=="string")try{const s=JSON.parse(e);r=JSON.stringify(s,null,2);}catch{r=e;}else r=JSON.stringify(e,null,2);return t.innerHTML=`
      <div class="detail-section">
        <div class="section-content">
          <pre>${r}</pre>
        </div>
      </div>
    `,t}exportRequests(){const e=v.getRequests(),t={exportTime:new Date().toISOString(),totalRequests:e.length,requests:e},r=new Blob([JSON.stringify(t,null,2)],{type:"application/json"}),s=URL.createObjectURL(r),a=document.createElement("a");a.href=s,a.download=`requests_${new Date().toISOString().slice(0,19).replace(/:/g,"-")}.json`,document.body.appendChild(a),a.click(),document.body.removeChild(a),URL.revokeObjectURL(s),this.events.onShowMessage("请求数据已导出","success");}getContainer(){return this.container}destroy(){this.container.parentNode&&this.container.parentNode.removeChild(this.container);}}class R extends HTMLElement{constructor(){super();n(this,"shadow");n(this,"floatingButton",null);n(this,"modal",null);n(this,"tabContainer",null);n(this,"accountPanel",null);n(this,"requestMonitor",null);this.shadow=this.attachShadow({mode:"closed"}),this.init();}init(){this.injectStyles(),v.start(),this.createFloatingButton();}injectStyles(){const t=document.createElement("style"),r=this.getStyles();t.textContent=r,this.shadow.appendChild(t);}getStyles(){return `
      /* 重置样式 */
      :host {
        all: initial;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'SF Pro Display', Roboto, 'Helvetica Neue', Arial, sans-serif;
        display: block;
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        z-index: 999999;
        pointer-events: none;
      }

      *, *::before, *::after {
        box-sizing: border-box;
      }

      /* 简洁现代化CSS变量系统 */
      :host {
        /* 主色调 - 简洁蓝色系 */
        --primary-50: #f0f9ff;
        --primary-100: #e0f2fe;
        --primary-200: #bae6fd;
        --primary-300: #7dd3fc;
        --primary-400: #38bdf8;
        --primary-500: #0ea5e9;
        --primary-600: #0284c7;
        --primary-700: #0369a1;
        --primary-800: #075985;
        --primary-900: #0c4a6e;

        /* 中性色 - 精简灰色系 */
        --gray-50: #fafafa;
        --gray-100: #f4f4f5;
        --gray-200: #e4e4e7;
        --gray-300: #d4d4d8;
        --gray-400: #a1a1aa;
        --gray-500: #71717a;
        --gray-600: #52525b;
        --gray-700: #3f3f46;
        --gray-800: #27272a;
        --gray-900: #18181b;

        /* 语义色彩 - 简化版 */
        --success-color: #22c55e;
        --danger-color: #ef4444;
        --warning-color: #f59e0b;
        --info-color: #3b82f6;

        /* 背景色 - 简洁版 */
        --bg-primary: #ffffff;
        --bg-secondary: var(--gray-50);
        --bg-glass: rgba(255, 255, 255, 0.95);
        --bg-overlay: rgba(0, 0, 0, 0.3);

        /* 文字色 - 简化版 */
        --text-primary: var(--gray-900);
        --text-secondary: var(--gray-600);
        --text-muted: var(--gray-400);
        --text-inverse: #ffffff;

        /* 边框色 - 简化版 */
        --border-light: var(--gray-200);
        --border-medium: var(--gray-300);

        /* 圆角系统 - 统一化 */
        --radius-sm: 0.375rem;
        --radius-md: 0.5rem;
        --radius-lg: 0.75rem;
        --radius-xl: 1rem;
        --radius-full: 9999px;

        /* 阴影系统 - 简化版 */
        --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
        --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
        --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
        --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

        /* 间距系统 - 精简版 */
        --space-1: 0.25rem;
        --space-2: 0.5rem;
        --space-3: 0.75rem;
        --space-4: 1rem;
        --space-5: 1.25rem;
        --space-6: 1.5rem;
        --space-8: 2rem;

        /* 动画缓动 - 优化版 */
        --ease-out: cubic-bezier(0.0, 0.0, 0.2, 1);
        --ease-in-out: cubic-bezier(0.4, 0.0, 0.2, 1);
        --ease-spring: cubic-bezier(0.34, 1.56, 0.64, 1);
      }

      /* 简洁悬浮按钮 */
      .floating-button {
        position: fixed;
        top: 50%;
        left: -16px;
        transform: translateY(-50%);
        z-index: 10000;
        width: 44px;
        height: 44px;
        background: var(--primary-600);
        border: none;
        border-radius: var(--radius-full);
        cursor: pointer;
        transition: all 0.3s var(--ease-out);
        box-shadow: var(--shadow-md);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--text-inverse);
        pointer-events: auto;
      }

      .floating-button:hover {
        left: 0;
        transform: translateY(-50%) scale(1.02);
        box-shadow: var(--shadow-lg);
        background: var(--primary-500);
      }

      .floating-button:active {
        transform: translateY(-50%) scale(0.98);
        transition: all 0.1s var(--ease-in-out);
      }

      .floating-button.dragging {
        transition: none;
        box-shadow: var(--shadow-xl);
      }

      .floating-button .icon {
        width: 18px;
        height: 18px;
        fill: currentColor;
        transition: transform 0.2s var(--ease-out);
      }

      .floating-button:hover .icon {
        transform: rotate(90deg);
      }

      /* 简洁模态框 */
      .modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: var(--bg-overlay);
        backdrop-filter: blur(12px);
        -webkit-backdrop-filter: blur(12px);
        z-index: 10001;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s var(--ease-out);
        pointer-events: auto;
        padding: var(--space-4);
      }

      .modal-overlay.show {
        opacity: 1;
        visibility: visible;
      }

      .modal {
        background: var(--bg-glass);
        backdrop-filter: blur(8px);
        -webkit-backdrop-filter: blur(8px);
        border: 1px solid var(--border-light);
        border-radius: var(--radius-xl);
        box-shadow: var(--shadow-xl);
        width: 100%;
        max-width: 900px;
        max-height: 85vh;
        overflow: hidden;
        transform: scale(0.95) translateY(10px);
        transition: all 0.3s var(--ease-spring);
        position: relative;
      }

      .modal-overlay.show .modal {
        transform: scale(1) translateY(0);
      }

      /* 简洁模态框头部 */
      .modal-header {
        padding: var(--space-5) var(--space-6) var(--space-4);
        border-bottom: 1px solid var(--border-light);
        display: flex;
        align-items: center;
        justify-content: space-between;
        min-height: 64px;
        background: var(--bg-secondary);
      }

      .modal-header h2 {
        margin: 0;
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--text-primary);
      }

      /* 简洁标签页系统 */
      .header-tabs {
        display: flex;
        gap: var(--space-2);
        flex: 1;
        background: var(--bg-primary);
        border-radius: var(--radius-lg);
        padding: var(--space-1);
      }

      .header-tab {
        padding: var(--space-2) var(--space-4);
        background: none;
        border: none;
        cursor: pointer;
        font-size: 0.875rem;
        font-weight: 500;
        color: var(--text-secondary);
        border-radius: var(--radius-md);
        transition: all 0.2s var(--ease-out);
        flex: 1;
        text-align: center;
      }

      .header-tab.active {
        color: var(--text-inverse);
        background: var(--primary-600);
        box-shadow: var(--shadow-sm);
      }

      .header-tab:hover:not(.active) {
        color: var(--text-primary);
        background: var(--bg-secondary);
      }

      .close-btn {
        background: var(--bg-primary);
        border: 1px solid var(--border-light);
        width: 28px;
        height: 28px;
        border-radius: var(--radius-md);
        cursor: pointer;
        color: var(--text-secondary);
        transition: all 0.2s var(--ease-out);
        margin-left: var(--space-4);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1rem;
      }

      .close-btn:hover {
        background: var(--danger-color);
        border-color: var(--danger-color);
        color: var(--text-inverse);
      }

      /* 简洁模态框主体 */
      .modal-body {
        padding: var(--space-6);
        max-height: 65vh;
        overflow-y: auto;
        background: var(--bg-primary);
      }

      .modal-body::-webkit-scrollbar {
        width: 4px;
      }

      .modal-body::-webkit-scrollbar-track {
        background: var(--bg-secondary);
        border-radius: var(--radius-sm);
      }

      .modal-body::-webkit-scrollbar-thumb {
        background: var(--border-medium);
        border-radius: var(--radius-sm);
      }

      .modal-body::-webkit-scrollbar-thumb:hover {
        background: var(--primary-400);
      }
      /* 简洁标签页内容 */
      .tab-container.header-mode .tabs {
        display: none;
      }

      .tabs {
        display: flex;
        border-bottom: 1px solid var(--border-light);
        margin-bottom: var(--space-5);
        background: var(--bg-secondary);
        border-radius: var(--radius-md) var(--radius-md) 0 0;
        padding: 0 var(--space-2);
      }

      .tab {
        padding: var(--space-3) var(--space-5);
        background: none;
        border: none;
        cursor: pointer;
        font-size: 0.875rem;
        font-weight: 500;
        color: var(--text-secondary);
        border-bottom: 2px solid transparent;
        transition: all 0.2s var(--ease-out);
      }

      .tab.active {
        color: var(--primary-600);
        border-bottom-color: var(--primary-600);
      }

      .tab:hover:not(.active) {
        color: var(--text-primary);
        background: var(--bg-primary);
      }

      .tab-content {
        display: none;
        animation: fadeIn 0.2s var(--ease-out);
      }

      .tab-content.active {
        display: block;
      }

      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateY(10px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      /* 简洁表单系统 */
      .account-panel {
        padding: var(--space-4);
      }

      .form-section {
        margin-bottom: var(--space-5);
        padding: var(--space-4);
        background: var(--bg-secondary);
        border-radius: var(--radius-md);
        border: 1px solid var(--border-light);
      }

      .form-row {
        margin-bottom: var(--space-3);
      }

      .form-label {
        display: block;
        margin-bottom: var(--space-2);
        font-size: 0.875rem;
        font-weight: 500;
        color: var(--text-primary);
      }

      .form-input {
        width: 100%;
        padding: var(--space-3) var(--space-4);
        border: 1px solid var(--border-light);
        border-radius: var(--radius-md);
        font-size: 0.875rem;
        background: var(--bg-primary);
        color: var(--text-primary);
        transition: all 0.2s var(--ease-out);
      }

      .form-input:focus {
        outline: none;
        border-color: var(--primary-500);
        box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
      }

      .form-input::placeholder {
        color: var(--text-muted);
      }

      .form-actions {
        display: flex;
        gap: var(--space-3);
        margin-top: var(--space-4);
        flex-wrap: wrap;
      }

      /* 简洁按钮系统 */
      .btn, .action-btn {
        padding: var(--space-3) var(--space-5);
        border: 1px solid transparent;
        border-radius: var(--radius-md);
        font-size: 0.875rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s var(--ease-out);
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: var(--space-2);
        text-decoration: none;
      }

      .btn:active, .action-btn:active {
        transform: scale(0.98);
      }

      .btn:disabled, .action-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        transform: none;
      }

      /* 主要按钮 */
      .btn.btn-primary, .action-btn.primary {
        background: var(--primary-600);
        color: var(--text-inverse);
        box-shadow: var(--shadow-sm);
      }

      .btn.btn-primary:hover:not(:disabled), .action-btn.primary:hover:not(:disabled) {
        background: var(--primary-500);
        box-shadow: var(--shadow-md);
      }

      /* 次要按钮 */
      .btn.btn-secondary, .action-btn.secondary {
        background: var(--bg-secondary);
        color: var(--text-primary);
        border-color: var(--border-light);
      }

      .btn.btn-secondary:hover:not(:disabled), .action-btn.secondary:hover:not(:disabled) {
        background: var(--bg-primary);
        border-color: var(--border-medium);
      }

      /* 危险按钮 */
      .btn.btn-danger, .action-btn.danger {
        background: var(--danger-color);
        color: var(--text-inverse);
        box-shadow: var(--shadow-sm);
      }

      .btn.btn-danger:hover:not(:disabled), .action-btn.danger:hover:not(:disabled) {
        background: #dc2626;
        box-shadow: var(--shadow-md);
      }

      /* 按钮图标 */
      .btn-icon {
        font-size: 1rem;
      }

      /* 按钮文本 */
      .btn-text {
        display: inline;
      }

      /* 小尺寸按钮 */
      .btn.btn-sm {
        padding: var(--space-2) var(--space-3);
        font-size: 0.75rem;
        border-radius: var(--radius-sm);
      }

      /* 按钮组 */
      .btn-group {
        display: flex;
        gap: var(--space-3);
        margin-top: var(--space-5);
        flex-wrap: wrap;
      }

      /* Token显示区域 */
      .token-display {
        margin-bottom: var(--space-3);
      }

      .token-label {
        font-size: 0.875rem;
        font-weight: 500;
        color: var(--text-primary);
        margin-bottom: var(--space-2);
      }

      .token-value {
        padding: var(--space-3);
        background: var(--bg-primary);
        border: 1px solid var(--border-light);
        border-radius: var(--radius-md);
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 0.75rem;
        color: var(--text-secondary);
        word-break: break-all;
        cursor: pointer;
        transition: all 0.2s var(--ease-out);
      }

      .token-value:hover {
        background: var(--bg-secondary);
        border-color: var(--border-medium);
      }

      /* 简洁加载状态 */
      .loading {
        display: inline-block;
        width: 16px;
        height: 16px;
        border: 2px solid var(--border-light);
        border-top: 2px solid var(--primary-500);
        border-radius: var(--radius-full);
        animation: spin 0.8s linear infinite;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      /* 简洁工具类 */
      .text-center { text-align: center; }
      .text-right { text-align: right; }
      .text-left { text-align: left; }

      .mt-2 { margin-top: var(--space-2); }
      .mt-3 { margin-top: var(--space-3); }
      .mt-4 { margin-top: var(--space-4); }
      .mt-5 { margin-top: var(--space-5); }

      .mb-2 { margin-bottom: var(--space-2); }
      .mb-3 { margin-bottom: var(--space-3); }
      .mb-4 { margin-bottom: var(--space-4); }
      .mb-5 { margin-bottom: var(--space-5); }

      .text-xs { font-size: 0.75rem; }
      .text-sm { font-size: 0.875rem; }
      .text-base { font-size: 1rem; }

      .font-normal { font-weight: 400; }
      .font-medium { font-weight: 500; }
      .font-semibold { font-weight: 600; }

      .text-primary { color: var(--text-primary); }
      .text-secondary { color: var(--text-secondary); }
      .text-muted { color: var(--text-muted); }
      .text-success { color: var(--success-color); }
      .text-danger { color: var(--danger-color); }
      .text-warning { color: var(--warning-color); }
      .text-info { color: var(--info-color); }

      .rounded-sm { border-radius: var(--radius-sm); }
      .rounded-md { border-radius: var(--radius-md); }
      .rounded-lg { border-radius: var(--radius-lg); }

      .shadow-sm { box-shadow: var(--shadow-sm); }
      .shadow-md { box-shadow: var(--shadow-md); }

      /* 简洁请求监控面板 */
      .request-monitor {
        background: var(--bg-primary);
        border: 1px solid var(--border-light);
        border-radius: var(--radius-lg);
        padding: var(--space-5);
      }

      .request-monitor .monitor-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: var(--space-5);
        padding: var(--space-3);
        background: var(--bg-secondary);
        border-radius: var(--radius-md);
      }

      .request-monitor .monitor-stats {
        display: flex;
        gap: var(--space-3);
        font-size: 0.75rem;
        color: var(--text-secondary);
      }

      .request-monitor .stat-item {
        display: flex;
        align-items: center;
        gap: var(--space-2);
        padding: var(--space-2) var(--space-3);
        background: var(--bg-primary);
        border: 1px solid var(--border-light);
        border-radius: var(--radius-sm);
      }

      .request-monitor .stat-dot {
        width: 8px;
        height: 8px;
        border-radius: var(--radius-full);
      }

      .request-monitor .stat-dot.success {
        background: var(--success-color);
      }
      .request-monitor .stat-dot.error {
        background: var(--danger-color);
      }
      .request-monitor .stat-dot.pending {
        background: var(--warning-color);
      }

      .request-monitor .monitor-controls {
        display: flex;
        gap: var(--space-2);
      }

      .request-monitor .request-filters {
        display: flex;
        gap: var(--space-3);
        margin-bottom: var(--space-5);
        padding: var(--space-3);
        background: var(--bg-secondary);
        border-radius: var(--radius-md);
      }

      .request-monitor .filter-input {
        flex: 1;
        padding: var(--space-2) var(--space-3);
        border: 1px solid var(--border-light);
        border-radius: var(--radius-sm);
        font-size: 0.75rem;
        background: var(--bg-primary);
        color: var(--text-primary);
        transition: all 0.2s var(--ease-out);
      }

      .request-monitor .filter-input:focus {
        outline: none;
        border-color: var(--primary-500);
        box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
      }

      .request-monitor .filter-select {
        padding: var(--space-2) var(--space-3);
        border: 1px solid var(--border-light);
        border-radius: var(--radius-sm);
        font-size: 0.75rem;
        background: var(--bg-primary);
        color: var(--text-primary);
        transition: all 0.2s var(--ease-out);
      }

      .request-monitor .request-list {
        max-height: 400px;
        overflow-y: auto;
        border: 1px solid var(--border-light);
        border-radius: var(--radius-md);
        background: var(--bg-primary);
      }

      .request-monitor .request-list::-webkit-scrollbar {
        width: 4px;
      }

      .request-monitor .request-list::-webkit-scrollbar-track {
        background: var(--bg-secondary);
        border-radius: var(--radius-sm);
      }

      .request-monitor .request-list::-webkit-scrollbar-thumb {
        background: var(--border-medium);
        border-radius: var(--radius-sm);
      }

      .request-monitor .request-item {
        padding: var(--space-3);
        border-bottom: 1px solid var(--border-light);
        cursor: pointer;
        transition: all 0.2s var(--ease-out);
      }

      .request-monitor .request-item:hover {
        background: var(--bg-secondary);
      }

      .request-monitor .request-item:last-child {
        border-bottom: none;
      }

      .request-monitor .request-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 0.25rem;
      }

      .request-monitor .request-method {
        padding: var(--space-1) var(--space-2);
        border-radius: var(--radius-sm);
        font-size: 0.625rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        border: 1px solid transparent;
        transition: all 0.2s var(--ease-out);
      }

      .request-monitor .request-method.GET {
        background: var(--info-color);
        color: white;
      }
      .request-monitor .request-method.POST {
        background: var(--success-color);
        color: white;
      }
      .request-monitor .request-method.PUT {
        background: var(--warning-color);
        color: white;
      }
      .request-monitor .request-method.DELETE {
        background: var(--danger-color);
        color: white;
      }
      .request-monitor .request-method.PATCH {
        background: var(--primary-600);
        color: white;
      }

      .request-monitor .request-status {
        display: flex;
        align-items: center;
        gap: var(--space-2);
        font-size: 0.75rem;
      }

      .request-monitor .status-code {
        padding: var(--space-1) var(--space-2);
        border-radius: var(--radius-sm);
        font-weight: 500;
        font-size: 0.625rem;
        border: 1px solid transparent;
      }

      .request-monitor .status-code.success {
        background: rgba(34, 197, 94, 0.1);
        color: var(--success-color);
        border-color: rgba(34, 197, 94, 0.2);
      }
      .request-monitor .status-code.error {
        background: rgba(239, 68, 68, 0.1);
        color: var(--danger-color);
        border-color: rgba(239, 68, 68, 0.2);
      }
      .request-monitor .status-code.pending {
        background: rgba(245, 158, 11, 0.1);
        color: var(--warning-color);
        border-color: rgba(245, 158, 11, 0.2);
      }

      .request-monitor .request-time {
        color: var(--text-muted);
        font-size: 0.625rem;
        background: var(--bg-secondary);
        padding: var(--space-1) var(--space-2);
        border-radius: var(--radius-sm);
      }

      .request-monitor .request-url {
        font-size: 0.75rem;
        color: var(--text-primary);
        word-break: break-all;
        word-wrap: break-word;
        overflow-wrap: break-word;
        margin-bottom: var(--space-2);
        overflow-x: hidden;
        max-width: 100%;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        background: var(--bg-secondary);
        padding: var(--space-2);
        border-radius: var(--radius-sm);
      }

      .request-monitor .request-meta {
        display: flex;
        justify-content: space-between;
        font-size: 0.625rem;
        color: var(--text-secondary);
        margin-top: var(--space-2);
      }

      .request-monitor .request-size,
      .request-monitor .request-duration {
        background: var(--bg-secondary);
        padding: var(--space-1) var(--space-2);
        border-radius: var(--radius-sm);
        display: flex;
        align-items: center;
        gap: var(--space-1);
      }

      .request-monitor .request-size::before {
        content: "📦";
        font-size: 0.75rem;
      }

      .request-monitor .request-duration::before {
        content: "⏱️";
        font-size: 0.75rem;
      }

      .request-monitor .empty-state {
        text-align: center;
        padding: var(--space-8);
        color: var(--text-secondary);
        background: var(--bg-secondary);
        border-radius: var(--radius-md);
      }

      .request-monitor .empty-icon {
        font-size: 3rem;
        margin-bottom: var(--space-3);
        opacity: 0.5;
      }

      .request-monitor .empty-text {
        font-size: 0.875rem;
        font-weight: 500;
      }
    `}createFloatingButton(){this.floatingButton=new S(()=>{this.showModal();});const t=this.floatingButton.getElement();this.shadow.appendChild(t);}showModal(){if(this.modal){this.modal.show();return}this.modal=new k({width:800,height:650,showTitle:!1,container:this.shadow,onClose:()=>{this.cleanup();}}),this.tabContainer=new q(!0);const t=this.modal.getModal().querySelector(".header-tabs");t&&this.tabContainer.setTabsContainer(t);const r={account:"",password:"Authine@123456",token:"",loading:!1};this.accountPanel=new L(r,{onAccountLogin:async s=>{},onTokenLogin:s=>{},onClearData:()=>{},onShowMessage:(s,a)=>{}}),this.requestMonitor=new M({onShowMessage:(s,a)=>{}}),this.tabContainer.addTab("account","账号切换",this.accountPanel.getContainer()),this.tabContainer.addTab("monitor","请求监控",this.requestMonitor.getContainer()),this.modal.setContent(this.tabContainer.getContainer()),this.modal.show();}cleanup(){this.modal=null,this.tabContainer=null,this.accountPanel=null,this.requestMonitor=null;}}customElements.define("account-change",R);function w(){if(document.querySelector("account-change"))return;const l=document.createElement("account-change");document.body.appendChild(l);}document.readyState==="loading"?document.addEventListener("DOMContentLoaded",w):w();

})();