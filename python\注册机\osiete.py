import time
import requests
import uuid
import random
import string

TURNSTILE_URL = "http://turnstile.yun0.me" # 自行搭建的turnstile solver服务
API_KEY = "mk_ilL2KDEuaS845l4IqaXC3stbQqkGHL5o" # mail.eleme.uk的api

def create_task():
    url = f"{TURNSTILE_URL}/turnstile"
    resp = requests.get(url, params={
        "url": "https://oshiete.ai/email_confirmation",
        "sitekey": "0x4AAAAAABGR2exxRproizri",
        "action": "request_registration_link"
    })
    print(resp.json())
    return resp.json()['task_id']


def get_result(task_id):
    url = f"{TURNSTILE_URL}/result"
    resp = requests.get(url, params={
        "id": task_id
    })
    if 'value' in resp.text:
        return resp.json()['value']
    return None

def solve_turnstile():
    task_id = create_task()
    while True:
        result = get_result(task_id)
        if result:
            return result
            break
        time.sleep(1)

def generate_random_string(length=10):
    characters = string.ascii_letters + string.digits
    random_string = ''.join(random.choice(characters) for _ in range(length))
    return random_string


def get_email():
    url = "https://mail.eleme.uk/api/emails/generate"
    headers = {
        "X-API-Key": API_KEY,
        "Content-Type": "application/json"
    }
    data = {
        "name": generate_random_string(8),
        "expiryTime": 3600000,
        "domain": "eleme.uk"
    }

    try:
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()  # 检查响应状态
        email_data = response.json()
        return email_data.get("email"), email_data.get("id")  # 假设API返回包含email字段的JSON
    except requests.exceptions.RequestException as e:
        print(f"获取邮箱时出错: {e}")
        return None

def get_code(id: str):
    url = f"https://mail.eleme.uk/api/emails/{id}"
    headers = {
        "X-API-Key": API_KEY
    }

    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        emails_data = response.json()
        # 提取验证码逻辑可能需要根据实际邮件内容调整
        for email in emails_data['messages']:
            if 'yGMO' in email['subject']:
                # 获取邮件内容的详细信息
                message_id = email['id']
                url_message = f"https://mail.eleme.uk/api/emails/{id}/{message_id}"
                message_response = requests.get(url_message, headers=headers)
                message_response.raise_for_status()
                message_data = message_response.json()
                return message_data['message']['html'].split('code=')[1].split('<br>')[0]
    except requests.exceptions.RequestException as e:
        print(f"获取验证码时出错: {e}")
        return None

def send_email(email: str):
    url = "https://graphql.oshiete.ai/graphql"
    resp = requests.post(url, json={
        "operationName": "RequestRegistrationLink",
        "variables": {
            "email": email,
            "turnstileToken": solve_turnstile()
        },
        "query": """
        mutation RequestRegistrationLink($email: String!, $turnstileToken: String!) {
            requestRegistrationLink(email: $email, turnstileToken: $turnstileToken)
        }
        """
    })

def register(code: str):
    url = "https://graphql.oshiete.ai/graphql"
    resp = requests.post(url, json={
        "operationName": "RegisterUser",
        "variables": {
            "code": code,
            "dti": str(uuid.uuid4()),
            "password": "Aa123321."
        },
        "query": """
        mutation RegisterUser($code: String!, $password: String!, $dti: String) {
            registerUser(code: $code, password: $password, dti: $dti) {
                id
                __typename
            }
        }
        """
    })
    # 提取sessionId cookie
    session_id = resp.cookies.get('sessionId')
    return session_id

if __name__ == "__main__":
    email, id = get_email()
    send_email(email)
    code = None
    while not code:
        code = get_code(id)
        time.sleep(1)
    session_id = register(code)
    print(session_id)