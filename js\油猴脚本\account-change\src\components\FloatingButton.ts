// 悬浮按钮组件

export class FloatingButton {
  private element: HTMLElement;
  private isDragging = false;
  private dragOffset = { x: 0, y: 0 };
  private onClick: () => void;

  constructor(onClick: () => void) {
    this.onClick = onClick;
    this.createElement();
    this.bindEvents();
  }

  /**
   * 创建按钮元素
   */
  private createElement(): void {
    this.element = document.createElement('button');
    this.element.className = 'floating-button';
    this.element.innerHTML = `
      <svg class="icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" fill="currentColor"/>
      </svg>
    `;

    // 设置初始位置（只显示宽度的0.25）
    // 使用固定宽度20px计算，因为此时元素还未添加到DOM
    const buttonWidth = 20;
    const hiddenWidth = buttonWidth * 0.75; // 隐藏75%
    this.setPosition(window.innerHeight / 2, -hiddenWidth);
  }

  /**
   * 绑定事件
   */
  private bindEvents(): void {
    // 点击事件
    this.element.addEventListener('click', () => {
      if (!this.isDragging) {
        this.onClick();
      }
    });

    // 鼠标拖拽事件
    this.element.addEventListener('mousedown', this.handleMouseDown.bind(this));
    document.addEventListener('mousemove', this.handleMouseMove.bind(this));
    document.addEventListener('mouseup', this.handleMouseUp.bind(this));

    // 触摸拖拽事件（移动端支持）
    this.element.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: false });
    document.addEventListener('touchmove', this.handleTouchMove.bind(this), { passive: false });
    document.addEventListener('touchend', this.handleTouchEnd.bind(this));

    // 窗口大小变化时调整位置
    window.addEventListener('resize', this.handleResize.bind(this));
  }

  /**
   * 鼠标按下事件
   */
  private handleMouseDown(e: MouseEvent): void {
    e.preventDefault();
    this.startDrag(e.clientX, e.clientY);
  }

  /**
   * 鼠标移动事件
   */
  private handleMouseMove(e: MouseEvent): void {
    if (this.isDragging) {
      e.preventDefault();
      this.drag(e.clientX, e.clientY);
    }
  }

  /**
   * 鼠标释放事件
   */
  private handleMouseUp(): void {
    this.endDrag();
  }

  /**
   * 触摸开始事件
   */
  private handleTouchStart(e: TouchEvent): void {
    e.preventDefault();
    const touch = e.touches[0];
    this.startDrag(touch.clientX, touch.clientY);
  }

  /**
   * 触摸移动事件
   */
  private handleTouchMove(e: TouchEvent): void {
    if (this.isDragging) {
      e.preventDefault();
      const touch = e.touches[0];
      this.drag(touch.clientX, touch.clientY);
    }
  }

  /**
   * 触摸结束事件
   */
  private handleTouchEnd(): void {
    this.endDrag();
  }

  /**
   * 开始拖拽
   */
  private startDrag(clientX: number, clientY: number): void {
    this.isDragging = true;
    this.element.classList.add('dragging');

    const rect = this.element.getBoundingClientRect();
    this.dragOffset = {
      x: clientX - rect.left,
      y: clientY - rect.top
    };
  }

  /**
   * 拖拽中
   */
  private drag(clientX: number, clientY: number): void {
    const x = clientX - this.dragOffset.x;
    const y = clientY - this.dragOffset.y;

    // 限制在窗口范围内
    const maxX = window.innerWidth - this.element.offsetWidth;
    const maxY = window.innerHeight - this.element.offsetHeight;

    const constrainedX = Math.max(0, Math.min(x, maxX));
    const constrainedY = Math.max(0, Math.min(y, maxY));

    this.setPosition(constrainedY, constrainedX);
  }

  /**
   * 结束拖拽
   */
  private endDrag(): void {
    if (this.isDragging) {
      this.isDragging = false;
      this.element.classList.remove('dragging');

      // 自动吸附到屏幕边缘
      this.snapToEdge();
    }
  }

  /**
   * 自动吸附到屏幕边缘
   */
  private snapToEdge(): void {
    const rect = this.element.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const windowWidth = window.innerWidth;
    const hiddenWidth = rect.width * 0.75; // 隐藏75%宽度

    // 判断吸附到左边还是右边
    const targetLeft = centerX < windowWidth / 2
      ? -hiddenWidth  // 左边只显示25%
      : windowWidth - rect.width + hiddenWidth; // 右边只显示25%

    this.element.style.transition = 'left 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
    this.element.style.left = targetLeft + 'px';

    // 恢复过渡效果
    setTimeout(() => {
      this.element.style.transition = '';
    }, 300);
  }

  /**
   * 设置位置
   */
  private setPosition(top: number, left: number): void {
    this.element.style.top = top + 'px';
    this.element.style.left = left + 'px';
  }

  /**
   * 窗口大小变化处理
   */
  private handleResize(): void {
    const rect = this.element.getBoundingClientRect();
    const maxX = window.innerWidth - rect.width;
    const maxY = window.innerHeight - rect.height;

    // 确保按钮在可见范围内
    if (rect.left > maxX) {
      this.element.style.left = maxX + 'px';
    }
    if (rect.top > maxY) {
      this.element.style.top = maxY + 'px';
    }
  }

  /**
   * 显示按钮
   */
  show(): void {
    this.element.style.display = 'flex';
  }

  /**
   * 隐藏按钮
   */
  hide(): void {
    this.element.style.display = 'none';
  }

  /**
   * 设置加载状态
   */
  setLoading(loading: boolean): void {
    const button = this.element as HTMLButtonElement;
    if (loading) {
      button.innerHTML = `
        <div class="loading"></div>
      `;
      button.disabled = true;
    } else {
      button.innerHTML = `
        <svg class="icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" fill="currentColor"/>
        </svg>
      `;
      button.disabled = false;
    }
  }

  /**
   * 获取DOM元素
   */
  getElement(): HTMLElement {
    return this.element;
  }

  /**
   * 销毁组件
   */
  destroy(): void {
    // 移除事件监听器
    document.removeEventListener('mousemove', this.handleMouseMove.bind(this));
    document.removeEventListener('mouseup', this.handleMouseUp.bind(this));
    document.removeEventListener('touchmove', this.handleTouchMove.bind(this));
    document.removeEventListener('touchend', this.handleTouchEnd.bind(this));
    window.removeEventListener('resize', this.handleResize.bind(this));

    // 移除DOM元素
    if (this.element.parentNode) {
      this.element.parentNode.removeChild(this.element);
    }
  }
}
