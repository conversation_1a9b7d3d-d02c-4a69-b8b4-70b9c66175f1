<?php

$config = [
    // "superapi" => [
    //     "url" => "https://api-demo.zyox.top/api/user/checkin",
    //     "cookie" => "session=MTc1MDAzOTQ5OHxEWDhFQVFMX2dBQUJFQUVRQUFEXzRQLUFBQWNHYzNSeWFXNW5EQTBBQzI5aGRYUm9YM04wWVhSbEJuTjBjbWx1Wnd3T0FBeGlWVXQyWVdkdVZsSkpUVGtHYzNSeWFXNW5EQVFBQW1sa0EybHVkQVFFQVA0RFZnWnpkSEpwYm1jTUNnQUlkWE5sY201aGJXVUdjM1J5YVc1bkRBMEFDMnhwYm5WNFpHOWZOREkzQm5OMGNtbHVad3dHQUFSeWIyeGxBMmx1ZEFRQ0FBSUdjM1J5YVc1bkRBZ0FCbk4wWVhSMWN3TnBiblFFQWdBQ0JuTjBjbWx1Wnd3SEFBVm5jbTkxY0FaemRISnBibWNNQ1FBSFpHVm1ZWFZzZEFaemRISnBibWNNQlFBRFlXWm1Cbk4wY21sdVp3d0dBQVJRWVhSS3z0IdMK68W2Zkqdj6rHTsUeqtT1y5Pn2A0jy6OWJQ7Whg==",
    //     "user_id" => "user-id: 427",
    // ],
    // "huanapi" => [
    //     "url" => "https://ai.huan666.de/api/user/check_in",
    //     "user_id" => "veloera-user: 1191",
    //     "cookie" => "session=MTc0OTA4ODY0MXxEWDhFQVFMX2dBQUJFQUVRQUFEX2xfLUFBQVVHYzNSeWFXNW5EQWdBQm5OMFlYUjFjd05wYm5RRUFnQUNCbk4wY21sdVp3d0hBQVZuY205MWNBWnpkSEpwYm1jTUNRQUhaR1ZtWVhWc2RBWnpkSEpwYm1jTUJBQUNhV1FEYVc1MEJBUUFfZ2xPQm5OMGNtbHVad3dLQUFoMWMyVnlibUZ0WlFaemRISnBibWNNRGdBTWJHbHVkWGhrYjE4eE1Ua3hCbk4wY21sdVp3d0dBQVJ5YjJ4bEEybHVkQVFDQUFJPXwxe40n3KfocrAgsj1hskvaGT7NliR56n1VUKXbyXQsrw==; HttpOnly; SameSite=Strict; Path=/; Max-Age=2592000; Expires=Sat, 05 Jul 2025 01:57:21 GMT",
    //     "login_url" => "https://ai.huan666.de/api/user/login?turnstile=",
    //     "account" => "linuxdo_1191:1qaz2wsx#" // 可选，格式为"账号:密码"
    // ],
//     "miaoapi" => [
//         "url" => "https://miaoapi.deno.dev/api/user/check_in",
//         "user_id" => "veloera-user: 142",
//         "cookie" => "
// session=MTc1MDIzMjIyNnxEWDhFQVFMX2dBQUJFQUVRQUFEX2x2LUFBQVVHYzNSeWFXNW5EQVFBQW1sa0EybHVkQVFFQVA0QkhBWnpkSEpwYm1jTUNnQUlkWE5sY201aGJXVUdjM1J5YVc1bkRBMEFDMnhwYm5WNFpHOWZNVFF5Qm5OMGNtbHVad3dHQUFSeWIyeGxBMmx1ZEFRQ0FBSUdjM1J5YVc1bkRBZ0FCbk4wWVhSMWN3TnBiblFFQWdBQ0JuTjBjbWx1Wnd3SEFBVm5jbTkxY0FaemRISnBibWNNQ1FBSFpHVm1ZWFZzZEE9PXyiDl3lFEGOLtYCKYiARyDdq3gUCo3mujWhHHGNlVyZrQ==",
//         "login_url" => "https://miaoapi.deno.dev/api/user/login?turnstile=",
//         "account" => "linuxdo_142:1qaz2wsx#" // 可选，格式为"账号:密码"
//     ],
//   "aiapi-asia" => [
//         "url" => "https://aiapi.asia/api/user/check_in", https://dynamic.zabc.net
//         "user_id" => "veloera-user: 58",
//         "cookie" => "
// session=MTc1MDIzMjIyNnxEWDhFQVFMX2dBQUJFQUVRQUFEX2x2LUFBQVVHYzNSeWFXNW5EQVFBQW1sa0EybHVkQVFFQVA0QkhBWnpkSEpwYm1jTUNnQUlkWE5sY201aGJXVUdjM1J5YVc1bkRBMEFDMnhwYm5WNFpHOWZNVFF5Qm5OMGNtbHVad3dHQUFSeWIyeGxBMmx1ZEFRQ0FBSUdjM1J5YVc1bkRBZ0FCbk4wWVhSMWN3TnBiblFFQWdBQ0JuTjBjbWx1Wnd3SEFBVm5jbTkxY0FaemRISnBibWNNQ1FBSFpHVm1ZWFZzZEE9PXyiDl3lFEGOLtYCKYiARyDdq3gUCo3mujWhHHGNlVyZrQ==",
//         "login_url" => "https://aiapi.asia/api/user/login?turnstile=",
//         "account" => "linuxdo_58:1qaz2wsx#" // 可选，格式为"账号:密码"
//     ],
//   "stone" => [
//         "url" => "http://49.12.130.109:45568/api/user/checkin",
//         "user_id" => "user-id: 383",
//         "cookie" => "session=MTc1MTU5MjM5MHxEWDhFQVFMX2dBQUJFQUVRQUFEX2pfLUFBQVVHYzNSeWFXNW5EQVFBQW1sa0EybHVkQVFFQVA0Q19nWnpkSEpwYm1jTUNnQUlkWE5sY201aGJXVUdjM1J5YVc1bkRBWUFCR05oYldVR2MzUnlhVzVuREFZQUJISnZiR1VEYVc1MEJBSUFBZ1p6ZEhKcGJtY01DQUFHYzNSaGRIVnpBMmx1ZEFRQ0FBSUdjM1J5YVc1bkRBY0FCV2R5YjNWd0JuTjBjbWx1Wnd3SkFBZGtaV1poZFd4MHyJuUSqF-7_bTLKS-phoGQOxtLcIH6XPhp02pJw-IzIEQ==",
//         "login_url" => "http://49.12.130.109:45568/api/user/login?turnstile=",
//         "account" => "came:1qaz2wsx#" // 可选，格式为"账号:密码"
//     ],
//   "bailai" => [
//      "url" => "https://wzxwhxcz-voapi.hf.space/api/user/clock_in?turnstile=",
//      "user_id" => "voapi-user: 601",
//      "cookie" => "session=MTc1MTI0NjQ4OHxEWDhFQVFMX2dBQUJFQUVRQUFEX3dfLUFBQVlHYzNSeWFXNW5EQWdBQm5OMFlYUjFjd05wYm5RRUFnQUNCbk4wY21sdVp3d0hBQVZuY205MWNBWnpkSEpwYm1jTUNRQUhaR1ZtWVhWc2RBWnpkSEpwYm1jTURRQUxiMkYxZEdoZmMzUmhkR1VHYzNSeWFXNW5EQTRBRERaRk5YSllWMEp0TVZGS1NBWnpkSEpwYm1jTUJBQUNhV1FEYVc1MEJBUUFfZ1N5Qm5OMGNtbHVad3dLQUFoMWMyVnlibUZ0WlFaemRISnBibWNNRFFBTGJHbHVkWGhrYjE4Mk1ERUdjM1J5YVc1bkRBWUFCSEp2YkdVRGFXNTBCQUlBQWc9PXw_3_Vo5BDWopslVb1bJIdZRHP3K2i7tVpRjYjvenu34A==",

//   ]
"zhongruanapi" => [
     "url" => "https://api.zhongruanapi.dpdns.org/api/user/clock_in?turnstile=",
     "user_id" => "voapi-user: 417",
     "cookie" => "session=MTc1MTI0NjQ4OHxEWDhFQVFMX2dBQUJFQUVRQUFEX3dfLUFBQVlHYzNSeWFXNW5EQWdBQm5OMFlYUjFjd05wYm5RRUFnQUNCbk4wY21sdVp3d0hBQVZuY205MWNBWnpkSEpwYm1jTUNRQUhaR1ZtWVhWc2RBWnpkSEpwYm1jTURRQUxiMkYxZEdoZmMzUmhkR1VHYzNSeWFXNW5EQTRBRERaRk5YSllWMEp0TVZGS1NBWnpkSEpwYm1jTUJBQUNhV1FEYVc1MEJBUUFfZ1N5Qm5OMGNtbHVad3dLQUFoMWMyVnlibUZ0WlFaemRISnBibWNNRFFBTGJHbHVkWGhrYjE4Mk1ERUdjM1J5YVc1bkRBWUFCSEp2YkdVRGFXNTBCQUlBQWc9PXw_3_Vo5BDWopslVb1bJIdZRHP3K2i7tVpRjYjvenu34A==",
    "login_url" => "https://api.zhongruanapi.dpdns.org/api/user/login?turnstile=",
        "account" => "linuxdo_417:1qaz2wsx#" // 可选，格式为"账号:密码"

  ]
];
function req_post($url, $data = [], $headers = [], $is_json = false)
{
    $ch = curl_init($url);

    // 基本设置
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);

    // 设置获取响应头
    curl_setopt($ch, CURLOPT_HEADER, true);

    // 根据请求类型设置数据格式
    if ($is_json) {
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        // 添加JSON内容类型到默认头
        $default_headers = [
            'Content-Type: application/json',
            'Accept: application/json',
            'Accept-Encoding: gzip, deflate, br'
        ];
    } else {
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
        // 表单内容类型
        $default_headers = [
            'Content-Type: application/x-www-form-urlencoded',
            'Accept: application/json',
            'Accept-Encoding: gzip, deflate, br'
        ];
    }

    // 添加 gzip 支持
    curl_setopt($ch, CURLOPT_ENCODING, 'gzip,deflate');

    // 设置请求头
    $headers = array_merge($default_headers, $headers);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

    // SSL设置
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

    $response = curl_exec($ch);

    // 处理错误
    if (curl_errno($ch)) {
        curl_close($ch);
        throw new Exception("Curl错误: " . curl_error($ch));
    }

    // 分离响应头和响应体
    $header_size = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
    $header = substr($response, 0, $header_size);
    $body = substr($response, $header_size);

    curl_close($ch);

    // 尝试解码 JSON 并以可读格式输出
    $decoded = json_decode($body, true);
    $result = [
        'headers' => $header,
        'body' => ($decoded !== null && json_last_error() === JSON_ERROR_NONE) ?
            json_encode($decoded, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) :
            $body
    ];

    return $result;
}

function makeRequest()
{
    global $config;  // 声明使用全局变量

    foreach ($config as $key => $value) {
        $url = $value['url'];
        $cookie = $value['cookie'];
        $headers = [
            'cookie: ' . $cookie,
            $value['user_id'],
            'content-type: application/x-www-form-urlencoded',
            'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'accept: application/json, text/plain, */*',
            'accept-language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'accept-encoding: gzip, deflate, br',
            'referer: https://api.ephone.ai/api/user/checkin',
            'origin: https://api.ephone.ai',
            'sec-fetch-mode: cors',
            'sec-fetch-site: same-site',
            'sec-fetch-dest: empty'
        ];

        // 检查是否存在account字段并且不为空
        if (isset($value['account']) && !empty($value['account']) && isset($value['login_url'])) {
            // 解析账号和密码
            list($username, $password) = explode(':', $value['account'], 2);

            // 构建登录请求参数
            $login_data = [
                'username' => $username,
                'password' => $password
            ];

            // 登录请求的通用头部
            $login_headers = [
                'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'accept: application/json, text/plain, */*',
                'accept-language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
                'accept-encoding: gzip, deflate, br',
                'origin: ' . parse_url($value['login_url'], PHP_URL_SCHEME) . '://' . parse_url($value['login_url'], PHP_URL_HOST)
            ];

            try {
                echo "登录账号: $username 中...\n";
                // 使用JSON格式发送登录请求
                $login_response = req_post($value['login_url'], $login_data, $login_headers, true);

                // 从响应头中提取cookie
                $response_headers = $login_response['headers'];
                $response_body = $login_response['body'];

                // 解析响应体JSON
                $login_result = json_decode($response_body, true);

                // 从响应头中提取Set-Cookie
                $new_cookie = '';
                if (preg_match('/Set-Cookie: (.*?);/i', $response_headers, $cookie_match)) {
                    $new_cookie = $cookie_match[1];
                    echo "从响应头获取新cookie: $new_cookie\n";

                    // 更新cookie
                    $cookie = $new_cookie;
                    $headers[0] = 'cookie: ' . $cookie;
                }
                // 如果响应头没有cookie，尝试从响应体获取
                elseif ($login_result && isset($login_result['data']['session'])) {
                    $new_cookie = $login_result['data']['session'];
                    echo "从响应体获取新cookie\n";

                    // 更新cookie
                    $cookie = $new_cookie;
                    $headers[0] = 'cookie: ' . $cookie;
                } else {
                    echo "登录失败，未找到cookie，使用原cookie\n";
                    echo "响应体: " . $response_body . "\n";
                }
            } catch (Exception $e) {
                echo "登录错误: " . $e->getMessage() . "\n";
                echo "使用原cookie继续\n";
            }
        }

        try {
            echo "向 $key 发送签到请求...\n";
            $response = req_post($url, [], $headers);
            // 确保输出时设置正确的 Content-Type
            header('Content-Type: text/plain; charset=utf-8');
            echo $response['body'] . "\n";
        } catch (Exception $e) {
            echo "签到错误: " . $e->getMessage() . "\n";
        }
    }
}

// 执行请求
makeRequest();
