// 请求拦截器

export interface RequestInfo {
  id: string;
  method: string;
  url: string;
  headers: Record<string, string>;
  body?: any;
  timestamp: number;
  status?: number;
  statusText?: string;
  responseHeaders?: Record<string, string>;
  responseBody?: any;
  duration?: number;
  error?: string;
  size?: number;
}

export type RequestListener = (request: RequestInfo | null) => void;

export class RequestInterceptor {
  private static instance: RequestInterceptor;
  private requests: Map<string, RequestInfo> = new Map();
  private listeners: Set<RequestListener> = new Set();
  private originalXHR: typeof XMLHttpRequest;
  private originalFetch: typeof fetch;
  private isIntercepting = false;

  private constructor() {
    this.originalXHR = window.XMLHttpRequest;
    this.originalFetch = window.fetch;
  }

  static getInstance(): RequestInterceptor {
    if (!RequestInterceptor.instance) {
      RequestInterceptor.instance = new RequestInterceptor();
    }
    return RequestInterceptor.instance;
  }

  /**
   * 开始拦截请求
   */
  start(): void {
    if (this.isIntercepting) return;

    this.interceptXHR();
    this.interceptFetch();
    this.isIntercepting = true;
    console.log('请求拦截器已启动');
  }

  /**
   * 停止拦截请求
   */
  stop(): void {
    if (!this.isIntercepting) return;

    window.XMLHttpRequest = this.originalXHR;
    window.fetch = this.originalFetch;
    this.isIntercepting = false;
    console.log('请求拦截器已停止');
  }

  /**
   * 添加请求监听器
   * @param listener 监听器函数
   */
  addListener(listener: RequestListener): void {
    this.listeners.add(listener);
  }

  /**
   * 移除请求监听器
   * @param listener 监听器函数
   */
  removeListener(listener: RequestListener): void {
    this.listeners.delete(listener);
  }

  /**
   * 获取所有请求记录
   * @returns 请求记录数组
   */
  getRequests(): RequestInfo[] {
    return Array.from(this.requests.values()).sort((a, b) => b.timestamp - a.timestamp);
  }

  /**
   * 清空请求记录
   */
  clearRequests(): void {
    this.requests.clear();
    this.notifyListeners();
  }

  /**
   * 获取请求统计
   * @returns 统计信息
   */
  getStats() {
    const requests = this.getRequests();
    const total = requests.length;
    const success = requests.filter(r => r.status && r.status >= 200 && r.status < 300).length;
    const error = requests.filter(r => r.status && r.status >= 400).length;
    const pending = requests.filter(r => !r.status).length;

    return { total, success, error, pending };
  }

  /**
   * 拦截XMLHttpRequest
   */
  private interceptXHR(): void {
    const self = this;

    window.XMLHttpRequest = function() {
      const xhr = new self.originalXHR();
      const requestId = self.generateId();
      let requestInfo: RequestInfo;

      // 重写open方法
      const originalOpen = xhr.open;
      xhr.open = function(method: string, url: string, ...args: any[]) {
        requestInfo = {
          id: requestId,
          method: method.toUpperCase(),
          url: url,
          headers: {},
          timestamp: Date.now()
        };

        return originalOpen.apply(this, [method, url, ...args]);
      };

      // 重写setRequestHeader方法
      const originalSetRequestHeader = xhr.setRequestHeader;
      xhr.setRequestHeader = function(name: string, value: string) {
        if (requestInfo) {
          requestInfo.headers[name] = value;
        }
        return originalSetRequestHeader.apply(this, [name, value]);
      };

      // 重写send方法
      const originalSend = xhr.send;
      xhr.send = function(body?: any) {
        if (requestInfo) {
          requestInfo.body = body;
          self.requests.set(requestId, requestInfo);
          console.log('XHR请求开始:', requestInfo);
          self.notifyListeners();
        }

        // 监听状态变化
        xhr.addEventListener('readystatechange', function() {
          if (xhr.readyState === 4 && requestInfo) {
            requestInfo.status = xhr.status;
            requestInfo.statusText = xhr.statusText;
            requestInfo.duration = Date.now() - requestInfo.timestamp;

            // 获取响应头
            const responseHeaders: Record<string, string> = {};
            const headerString = xhr.getAllResponseHeaders();
            if (headerString) {
              headerString.split('\r\n').forEach(line => {
                const [key, value] = line.split(': ');
                if (key && value) {
                  responseHeaders[key] = value;
                }
              });
            }
            requestInfo.responseHeaders = responseHeaders;

            // 获取响应体
            try {
              requestInfo.responseBody = xhr.responseText;
              requestInfo.size = xhr.responseText?.length || 0;
            } catch (e) {
              requestInfo.responseBody = '[无法读取响应体]';
            }

            self.requests.set(requestId, requestInfo);
            self.notifyListeners();
          }
        });

        // 监听错误
        xhr.addEventListener('error', function() {
          if (requestInfo) {
            requestInfo.error = '网络错误';
            requestInfo.duration = Date.now() - requestInfo.timestamp;
            self.requests.set(requestId, requestInfo);
            self.notifyListeners();
          }
        });

        return originalSend.apply(this, [body]);
      };

      return xhr;
    } as any;
  }

  /**
   * 拦截fetch
   */
  private interceptFetch(): void {
    const self = this;

    window.fetch = async function(input: globalThis.RequestInfo | URL, init?: RequestInit): Promise<Response> {
      const requestId = self.generateId();
      const startTime = Date.now();

      // 解析请求信息
      const url = typeof input === 'string' ? input : input.toString();
      const method = init?.method?.toUpperCase() || 'GET';
      const headers: Record<string, string> = {};

      if (init?.headers) {
        if (init.headers instanceof Headers) {
          init.headers.forEach((value, key) => {
            headers[key] = value;
          });
        } else if (Array.isArray(init.headers)) {
          init.headers.forEach(([key, value]) => {
            headers[key] = value;
          });
        } else {
          Object.entries(init.headers).forEach(([key, value]) => {
            headers[key] = value;
          });
        }
      }

      const requestInfo: RequestInfo = {
        id: requestId,
        method,
        url,
        headers,
        body: init?.body,
        timestamp: startTime
      };

      self.requests.set(requestId, requestInfo);
    console.log('Fetch请求开始:', requestInfo);
      self.notifyListeners();

      try {
        const response = await self.originalFetch.apply(window, [input, init]);

        // 更新请求信息
        requestInfo.status = response.status;
        requestInfo.statusText = response.statusText;
        requestInfo.duration = Date.now() - startTime;

        // 获取响应头
        const responseHeaders: Record<string, string> = {};
        response.headers.forEach((value: string, key: string) => {
          responseHeaders[key] = value;
        });
        requestInfo.responseHeaders = responseHeaders;

        // 克隆响应以读取body
        const clonedResponse = response.clone();
        try {
          const responseText = await clonedResponse.text();
          requestInfo.responseBody = responseText;
          requestInfo.size = responseText.length;
        } catch (e) {
          requestInfo.responseBody = '[无法读取响应体]';
        }

        self.requests.set(requestId, requestInfo);
        self.notifyListeners();

        return response;
      } catch (error) {
        requestInfo.error = error instanceof Error ? error.message : '请求失败';
        requestInfo.duration = Date.now() - startTime;
        self.requests.set(requestId, requestInfo);
        self.notifyListeners();
        throw error;
      }
    };
  }

  /**
   * 生成唯一ID
   * @returns 唯一ID
   */
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substring(2);
  }

  /**
   * 通知所有监听器
   */
  private notifyListeners(): void {
    this.listeners.forEach(listener => {
      try {
        // 检查是否有请求数据
        const requests = this.getRequests();
        const latestRequest = requests.length > 0 ? requests[0] : null;
        listener(latestRequest);
      } catch (error) {
        console.error('请求监听器执行失败:', error);
      }
    });
  }
}

// 导出单例实例
export const requestInterceptor = RequestInterceptor.getInstance();
