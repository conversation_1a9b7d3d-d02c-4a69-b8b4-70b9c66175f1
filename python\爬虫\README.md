# LXC.WIKI 多线程爬虫系统

一个高效的多线程爬虫系统，用于监控 LXC.WIKI 的产品库存状态，并在库存变化时发送通知。

## 功能特性

- ✅ **多URL支持**：支持同时监控多个不同类型的产品URL
- ✅ **多线程并发爬取**：支持多线程同时爬取多个分组，提高效率
- ✅ **智能分组筛选**：支持只取最后N个分组，减少请求数量
- ✅ **库存状态监控**：实时监控产品库存状态变化
- ✅ **智能通知系统**：支持多种通知方式（微信、钉钉、邮件等）
- ✅ **历史数据对比**：自动对比历史数据，检测库存变化
- ✅ **持续监控模式**：支持定时自动监控
- ✅ **灵活配置**：支持多种配置方式和运行模式

## 文件结构

```
python/爬虫/
├── lxc.wiki.py          # 主爬虫程序
├── config.py            # 配置文件
├── notify.py            # 通知模块
└── README.md            # 使用说明
```

## 安装依赖

```bash
pip install requests beautifulsoup4
```

## 配置说明

### 1. 基本配置 (config.py)

```python
# 爬虫基本配置
CRAWLER_CONFIG = {
    'max_workers': 5,        # 最大并发线程数
    'request_delay': 1.5,    # 请求间隔（秒）
    'timeout': 30,           # 请求超时时间（秒）
    'save_history': True,    # 是否保存历史数据
}

# 目标URL配置
TARGET_URLS = [
    {
        'url': 'https://www.lxc.wiki/cart?fid=1',
        'name': 'VPS产品(fid=1)',
        'take_last': 3,  # 只取最后3个分组
        'enabled': True
    },
    {
        'url': 'https://www.lxc.wiki/cart?fid=4',
        'name': '独立服务器(fid=4)',
        'take_last': 3,  # 只取最后3个分组
        'enabled': True
    },
    {
        'url': 'https://www.lxc.wiki/cart?fid=3&gid=58',
        'name': '特殊产品(fid=3&gid=58)',
        'take_last': 3,  # 只取最后3个分组
        'enabled': True
    }
]

# 通知配置
NOTIFICATION_CONFIG = {
    'enable_notifications': True,      # 是否启用通知
    'notify_on_stock_change': True,    # 库存变化时是否通知
    'notify_on_available_stock': True, # 发现有库存时是否通知
    'notify_summary': True,            # 是否发送汇总通知
}
```

### 2. 通知服务配置

在 `config.py` 中配置你需要的通知服务：

```python
NOTIFY_SERVICES = {
    # 控制台输出
    'CONSOLE': True,

    # Server酱（微信推送）
    'PUSH_KEY': 'your_server_chan_key',

    # 钉钉机器人
    'DD_BOT_TOKEN': 'your_dingtalk_token',
    'DD_BOT_SECRET': 'your_dingtalk_secret',

    # 企业微信机器人
    'QYWX_KEY': 'your_wechat_work_key',

    # Telegram Bot
    'TG_BOT_TOKEN': 'your_telegram_bot_token',
    'TG_USER_ID': 'your_telegram_user_id',

    # 邮件通知
    'SMTP_SERVER': 'smtp.qq.com:587',
    'SMTP_EMAIL': '<EMAIL>',
    'SMTP_PASSWORD': 'your_email_password',
    'SMTP_NAME': 'LXC监控',
    'SMTP_SSL': 'true',
}
```

### 3. 环境变量配置

也可以通过环境变量配置通知服务：

```bash
export PUSH_KEY="your_server_chan_key"
export DD_BOT_TOKEN="your_dingtalk_token"
export DD_BOT_SECRET="your_dingtalk_secret"
```

## 使用方法

### 1. 基本使用

```bash
# 运行一次爬取
python lxc.wiki.py

# 禁用通知运行
python lxc.wiki.py --no-notify

# 快速模式（10线程，0.5秒延迟）
python lxc.wiki.py --fast

# 持续监控模式
python lxc.wiki.py --monitor

# 显示帮助信息
python lxc.wiki.py --help
```

### 2. 持续监控

持续监控模式会按照配置的间隔时间自动运行爬虫：

```bash
python lxc.wiki.py --monitor
```

默认间隔为5分钟，可在 `config.py` 中修改：

```python
MONITOR_CONFIG = {
    'check_interval': 300,  # 检查间隔（秒）
}
```

## 输出说明

### 1. 控制台输出

```
🚀 开始多线程爬取: https://www.lxc.wiki/cart?fid=1
配置: 最大并发数=5, 请求间隔=1.5秒
📋 共找到 22 个分组
✓ 洛杉磯優化 1区: 找到 3 个产品，其中 0 个有库存
📊 进度: 1/22 - 洛杉磯優化 1区
...
🎉 爬取完成!
📈 统计信息:
  - 总分组数: 22
  - 总产品数: 50
  - 有库存产品: 0
  - 数据文件: lxc_wiki_data_20250721_155553.json
```

### 2. 数据文件

爬取结果会保存为 JSON 文件，包含完整的产品信息：

```json
{
  "groups": [
    {
      "group_name": "洛杉磯優化 1区",
      "group_url": "https://www.lxc.wiki/cart?fid=1&gid=1",
      "products": [
        {
          "name": "洛杉磯 1區 Nano",
          "description": "CPU：1 核記憶體：256 M...",
          "stock": "已售完",
          "price": "¥ 1.00 元 / 月",
          "group_name": "洛杉磯優化 1区",
          "group_url": "https://www.lxc.wiki/cart?fid=1&gid=1"
        }
      ],
      "product_count": 3,
      "available_count": 0
    }
  ],
  "total_groups": 22,
  "total_products": 50,
  "total_available": 0,
  "crawl_time": "2025-07-21 15:55:53"
}
```

### 3. 通知消息

当检测到库存变化时，会发送通知：

```
🎉 LXC.WIKI 库存补货通知

产品名称: 洛杉磯 1區 Nano
分组: 洛杉磯優化 1区
当前库存: 库存充足
价格: ¥ 1.00 元 / 月
描述: CPU：1 核記憶體：256 M...

链接: https://www.lxc.wiki/cart?fid=1&gid=1
时间: 2025-07-21 15:57:37
```

## 高级功能

### 1. 自定义过滤

在 `config.py` 中配置过滤条件：

```python
FILTER_CONFIG = {
    'exclude_groups': ['测试分组'],     # 排除的分组
    'include_groups': [],              # 只包含的分组
    'exclude_keywords': ['测试', 'test'], # 排除关键词
    'min_price': 0,                    # 最低价格
    'max_price': 999999,               # 最高价格
}
```

### 2. 监控关键词

配置重点监控的产品关键词：

```python
MONITOR_CONFIG = {
    'monitor_keywords': ['Nano', 'Mini'],  # 优先通知包含这些词的产品
    'price_threshold': 5,                  # 低于此价格优先通知
}
```

## 注意事项

1. **请求频率**：建议设置合理的请求间隔，避免对服务器造成压力
2. **通知配置**：首次使用前请正确配置通知服务
3. **网络环境**：确保网络连接稳定，支持访问目标网站
4. **数据存储**：历史数据文件会自动创建，用于对比库存变化

## 故障排除

### 1. 通知不工作

- 检查 `config.py` 中的通知服务配置
- 确认 `notify.py` 文件存在
- 检查网络连接和服务商API限制

### 2. 爬取失败

- 检查网络连接
- 确认目标网站可访问
- 适当增加请求超时时间

### 3. 性能问题

- 减少并发线程数 (`max_workers`)
- 增加请求间隔 (`request_delay`)
- 检查系统资源使用情况

## 更新日志

- **v2.0** - 添加多线程支持和智能通知系统
- **v1.0** - 基础爬虫功能

## 许可证

本项目仅供学习和个人使用，请遵守相关网站的使用条款。
