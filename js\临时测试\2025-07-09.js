import req from '../utils/request.js'
import crypto from 'crypto'

    ; (async () => {
        let email = '<EMAIL>', password = 'udesk2023'
        let { open_api_auth_token } = await req.post("https://1853180.s2.udesk.cn/open_api_v1/log_in", { json: { email: email, password: password } })
        // getEpochSecond
        let timestamp = Math.floor(Date.now() / 1000)
        console.log('timestamp:', timestamp)
        let nonce = 'occ' + timestamp
        let str = `${email}&${open_api_auth_token}&${timestamp}&${nonce}&v2`
        //sha-256 加密 str
        let hash = crypto.createHash('sha256').update(str).digest('hex')
        console.log('原始字符串:', str)
        console.log('SHA-256 加密结果:', hash)

        let resp = await req.get('https://1853180.s2.udesk.cn/open_api_v1/tickets', {
            searchParams: {
                email: email,
                timestamp: timestamp,
                nonce: nonce,
                sign: hash,
                sign_version: 'v2',
                page: 80000,
                per_page: 100
            }
        })

        console.log(JSON.stringify(resp))

    })()