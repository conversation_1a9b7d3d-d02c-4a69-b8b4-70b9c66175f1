import { FloatingButton } from './FloatingButton';
import { Mo<PERSON>, TabContainer } from './Modal';
import { AccountPanel } from './AccountPanel';
import { RequestMonitor } from './RequestMonitor';
import { requestInterceptor } from '../utils/interceptor';

/**
 * 账号切换主组件 - Shadow DOM实现
 * 使用Shadow DOM实现完全的样式隔离
 */
export class AccountChangeElement extends HTMLElement {
  private shadow: ShadowRoot;
  private floatingButton: FloatingButton | null = null;
  private modal: Modal | null = null;
  private tabContainer: TabContainer | null = null;
  private accountPanel: AccountPanel | null = null;
  private requestMonitor: RequestMonitor | null = null;

  constructor() {
    super();

    // 创建Shadow DOM
    this.shadow = this.attachShadow({ mode: 'closed' });

    // 初始化
    this.init();
  }

  /**
   * 初始化组件
   */
  private init(): void {
    // 注入样式
    this.injectStyles();

    // 启动请求拦截器
    requestInterceptor.start();

    // 创建悬浮按钮
    this.createFloatingButton();
  }

  /**
   * 注入样式到Shadow DOM
   */
  private injectStyles(): void {
    const style = document.createElement('style');
    const styles = this.getStyles();
    style.textContent = styles;
    this.shadow.appendChild(style);
  }

  /**
   * 获取所有样式 - 现代化重建版本
   */
  private getStyles(): string {
    return `
      /* 重置样式 */
      :host {
        all: initial;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'SF Pro Display', Roboto, 'Helvetica Neue', Arial, sans-serif;
        display: block;
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        z-index: 999999;
        pointer-events: none;
      }

      *, *::before, *::after {
        box-sizing: border-box;
      }

      /* 简洁现代化CSS变量系统 */
      :host {
        /* 主色调 - 简洁蓝色系 */
        --primary-50: #f0f9ff;
        --primary-100: #e0f2fe;
        --primary-200: #bae6fd;
        --primary-300: #7dd3fc;
        --primary-400: #38bdf8;
        --primary-500: #0ea5e9;
        --primary-600: #0284c7;
        --primary-700: #0369a1;
        --primary-800: #075985;
        --primary-900: #0c4a6e;

        /* 中性色 - 精简灰色系 */
        --gray-50: #fafafa;
        --gray-100: #f4f4f5;
        --gray-200: #e4e4e7;
        --gray-300: #d4d4d8;
        --gray-400: #a1a1aa;
        --gray-500: #71717a;
        --gray-600: #52525b;
        --gray-700: #3f3f46;
        --gray-800: #27272a;
        --gray-900: #18181b;

        /* 语义色彩 - 简化版 */
        --success-color: #22c55e;
        --danger-color: #ef4444;
        --warning-color: #f59e0b;
        --info-color: #3b82f6;

        /* 背景色 - 简洁版 */
        --bg-primary: #ffffff;
        --bg-secondary: var(--gray-50);
        --bg-glass: rgba(255, 255, 255, 0.95);
        --bg-overlay: rgba(0, 0, 0, 0.3);

        /* 文字色 - 简化版 */
        --text-primary: var(--gray-900);
        --text-secondary: var(--gray-600);
        --text-muted: var(--gray-400);
        --text-inverse: #ffffff;

        /* 边框色 - 简化版 */
        --border-light: var(--gray-200);
        --border-medium: var(--gray-300);

        /* 圆角系统 - 统一化 */
        --radius-sm: 0.375rem;
        --radius-md: 0.5rem;
        --radius-lg: 0.75rem;
        --radius-xl: 1rem;
        --radius-full: 9999px;

        /* 阴影系统 - 简化版 */
        --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
        --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
        --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
        --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

        /* 间距系统 - 精简版 */
        --space-1: 0.25rem;
        --space-2: 0.5rem;
        --space-3: 0.75rem;
        --space-4: 1rem;
        --space-5: 1.25rem;
        --space-6: 1.5rem;
        --space-8: 2rem;

        /* 动画缓动 - 优化版 */
        --ease-out: cubic-bezier(0.0, 0.0, 0.2, 1);
        --ease-in-out: cubic-bezier(0.4, 0.0, 0.2, 1);
        --ease-spring: cubic-bezier(0.34, 1.56, 0.64, 1);
      }

      /* 简洁悬浮按钮 */
      .floating-button {
        position: fixed;
        top: 50%;
        left: -16px;
        transform: translateY(-50%);
        z-index: 10000;
        width: 44px;
        height: 44px;
        background: var(--primary-600);
        border: none;
        border-radius: var(--radius-full);
        cursor: pointer;
        transition: all 0.3s var(--ease-out);
        box-shadow: var(--shadow-md);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--text-inverse);
        pointer-events: auto;
      }

      .floating-button:hover {
        left: 0;
        transform: translateY(-50%) scale(1.02);
        box-shadow: var(--shadow-lg);
        background: var(--primary-500);
      }

      .floating-button:active {
        transform: translateY(-50%) scale(0.98);
        transition: all 0.1s var(--ease-in-out);
      }

      .floating-button.dragging {
        transition: none;
        box-shadow: var(--shadow-xl);
      }

      .floating-button .icon {
        width: 18px;
        height: 18px;
        fill: currentColor;
        transition: transform 0.2s var(--ease-out);
      }

      .floating-button:hover .icon {
        transform: rotate(90deg);
      }

      /* 简洁模态框 */
      .modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: var(--bg-overlay);
        backdrop-filter: blur(12px);
        -webkit-backdrop-filter: blur(12px);
        z-index: 10001;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s var(--ease-out);
        pointer-events: auto;
        padding: var(--space-4);
      }

      .modal-overlay.show {
        opacity: 1;
        visibility: visible;
      }

      .modal {
        background: var(--bg-glass);
        backdrop-filter: blur(8px);
        -webkit-backdrop-filter: blur(8px);
        border: 1px solid var(--border-light);
        border-radius: var(--radius-xl);
        box-shadow: var(--shadow-xl);
        width: 100%;
        max-width: 900px;
        max-height: 85vh;
        overflow: hidden;
        transform: scale(0.95) translateY(10px);
        transition: all 0.3s var(--ease-spring);
        position: relative;
      }

      .modal-overlay.show .modal {
        transform: scale(1) translateY(0);
      }

      /* 简洁模态框头部 */
      .modal-header {
        padding: var(--space-5) var(--space-6) var(--space-4);
        border-bottom: 1px solid var(--border-light);
        display: flex;
        align-items: center;
        justify-content: space-between;
        min-height: 64px;
        background: var(--bg-secondary);
      }

      .modal-header h2 {
        margin: 0;
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--text-primary);
      }

      /* 简洁标签页系统 */
      .header-tabs {
        display: flex;
        gap: var(--space-2);
        flex: 1;
        background: var(--bg-primary);
        border-radius: var(--radius-lg);
        padding: var(--space-1);
      }

      .header-tab {
        padding: var(--space-2) var(--space-4);
        background: none;
        border: none;
        cursor: pointer;
        font-size: 0.875rem;
        font-weight: 500;
        color: var(--text-secondary);
        border-radius: var(--radius-md);
        transition: all 0.2s var(--ease-out);
        flex: 1;
        text-align: center;
      }

      .header-tab.active {
        color: var(--text-inverse);
        background: var(--primary-600);
        box-shadow: var(--shadow-sm);
      }

      .header-tab:hover:not(.active) {
        color: var(--text-primary);
        background: var(--bg-secondary);
      }

      .close-btn {
        background: var(--bg-primary);
        border: 1px solid var(--border-light);
        width: 28px;
        height: 28px;
        border-radius: var(--radius-md);
        cursor: pointer;
        color: var(--text-secondary);
        transition: all 0.2s var(--ease-out);
        margin-left: var(--space-4);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1rem;
      }

      .close-btn:hover {
        background: var(--danger-color);
        border-color: var(--danger-color);
        color: var(--text-inverse);
      }

      /* 简洁模态框主体 */
      .modal-body {
        padding: var(--space-6);
        max-height: 65vh;
        overflow-y: auto;
        background: var(--bg-primary);
      }

      .modal-body::-webkit-scrollbar {
        width: 4px;
      }

      .modal-body::-webkit-scrollbar-track {
        background: var(--bg-secondary);
        border-radius: var(--radius-sm);
      }

      .modal-body::-webkit-scrollbar-thumb {
        background: var(--border-medium);
        border-radius: var(--radius-sm);
      }

      .modal-body::-webkit-scrollbar-thumb:hover {
        background: var(--primary-400);
      }
      /* 简洁标签页内容 */
      .tab-container.header-mode .tabs {
        display: none;
      }

      .tabs {
        display: flex;
        border-bottom: 1px solid var(--border-light);
        margin-bottom: var(--space-5);
        background: var(--bg-secondary);
        border-radius: var(--radius-md) var(--radius-md) 0 0;
        padding: 0 var(--space-2);
      }

      .tab {
        padding: var(--space-3) var(--space-5);
        background: none;
        border: none;
        cursor: pointer;
        font-size: 0.875rem;
        font-weight: 500;
        color: var(--text-secondary);
        border-bottom: 2px solid transparent;
        transition: all 0.2s var(--ease-out);
      }

      .tab.active {
        color: var(--primary-600);
        border-bottom-color: var(--primary-600);
      }

      .tab:hover:not(.active) {
        color: var(--text-primary);
        background: var(--bg-primary);
      }

      .tab-content {
        display: none;
        animation: fadeIn 0.2s var(--ease-out);
      }

      .tab-content.active {
        display: block;
      }

      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateY(10px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      /* 简洁表单系统 */
      .account-panel {
        padding: var(--space-4);
      }

      .form-section {
        margin-bottom: var(--space-5);
        padding: var(--space-4);
        background: var(--bg-secondary);
        border-radius: var(--radius-md);
        border: 1px solid var(--border-light);
      }

      .form-row {
        margin-bottom: var(--space-3);
      }

      .form-label {
        display: block;
        margin-bottom: var(--space-2);
        font-size: 0.875rem;
        font-weight: 500;
        color: var(--text-primary);
      }

      .form-input {
        width: 100%;
        padding: var(--space-3) var(--space-4);
        border: 1px solid var(--border-light);
        border-radius: var(--radius-md);
        font-size: 0.875rem;
        background: var(--bg-primary);
        color: var(--text-primary);
        transition: all 0.2s var(--ease-out);
      }

      .form-input:focus {
        outline: none;
        border-color: var(--primary-500);
        box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
      }

      .form-input::placeholder {
        color: var(--text-muted);
      }

      .form-actions {
        display: flex;
        gap: var(--space-3);
        margin-top: var(--space-4);
        flex-wrap: wrap;
      }

      /* 简洁按钮系统 */
      .btn, .action-btn {
        padding: var(--space-3) var(--space-5);
        border: 1px solid transparent;
        border-radius: var(--radius-md);
        font-size: 0.875rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s var(--ease-out);
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: var(--space-2);
        text-decoration: none;
      }

      .btn:active, .action-btn:active {
        transform: scale(0.98);
      }

      .btn:disabled, .action-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        transform: none;
      }

      /* 主要按钮 */
      .btn.btn-primary, .action-btn.primary {
        background: var(--primary-600);
        color: var(--text-inverse);
        box-shadow: var(--shadow-sm);
      }

      .btn.btn-primary:hover:not(:disabled), .action-btn.primary:hover:not(:disabled) {
        background: var(--primary-500);
        box-shadow: var(--shadow-md);
      }

      /* 次要按钮 */
      .btn.btn-secondary, .action-btn.secondary {
        background: var(--bg-secondary);
        color: var(--text-primary);
        border-color: var(--border-light);
      }

      .btn.btn-secondary:hover:not(:disabled), .action-btn.secondary:hover:not(:disabled) {
        background: var(--bg-primary);
        border-color: var(--border-medium);
      }

      /* 危险按钮 */
      .btn.btn-danger, .action-btn.danger {
        background: var(--danger-color);
        color: var(--text-inverse);
        box-shadow: var(--shadow-sm);
      }

      .btn.btn-danger:hover:not(:disabled), .action-btn.danger:hover:not(:disabled) {
        background: #dc2626;
        box-shadow: var(--shadow-md);
      }

      /* 按钮图标 */
      .btn-icon {
        font-size: 1rem;
      }

      /* 按钮文本 */
      .btn-text {
        display: inline;
      }

      /* 小尺寸按钮 */
      .btn.btn-sm {
        padding: var(--space-2) var(--space-3);
        font-size: 0.75rem;
        border-radius: var(--radius-sm);
      }

      /* 按钮组 */
      .btn-group {
        display: flex;
        gap: var(--space-3);
        margin-top: var(--space-5);
        flex-wrap: wrap;
      }

      /* Token显示区域 */
      .token-display {
        margin-bottom: var(--space-3);
      }

      .token-label {
        font-size: 0.875rem;
        font-weight: 500;
        color: var(--text-primary);
        margin-bottom: var(--space-2);
      }

      .token-value {
        padding: var(--space-3);
        background: var(--bg-primary);
        border: 1px solid var(--border-light);
        border-radius: var(--radius-md);
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 0.75rem;
        color: var(--text-secondary);
        word-break: break-all;
        cursor: pointer;
        transition: all 0.2s var(--ease-out);
      }

      .token-value:hover {
        background: var(--bg-secondary);
        border-color: var(--border-medium);
      }

      /* 简洁加载状态 */
      .loading {
        display: inline-block;
        width: 16px;
        height: 16px;
        border: 2px solid var(--border-light);
        border-top: 2px solid var(--primary-500);
        border-radius: var(--radius-full);
        animation: spin 0.8s linear infinite;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      /* 简洁工具类 */
      .text-center { text-align: center; }
      .text-right { text-align: right; }
      .text-left { text-align: left; }

      .mt-2 { margin-top: var(--space-2); }
      .mt-3 { margin-top: var(--space-3); }
      .mt-4 { margin-top: var(--space-4); }
      .mt-5 { margin-top: var(--space-5); }

      .mb-2 { margin-bottom: var(--space-2); }
      .mb-3 { margin-bottom: var(--space-3); }
      .mb-4 { margin-bottom: var(--space-4); }
      .mb-5 { margin-bottom: var(--space-5); }

      .text-xs { font-size: 0.75rem; }
      .text-sm { font-size: 0.875rem; }
      .text-base { font-size: 1rem; }

      .font-normal { font-weight: 400; }
      .font-medium { font-weight: 500; }
      .font-semibold { font-weight: 600; }

      .text-primary { color: var(--text-primary); }
      .text-secondary { color: var(--text-secondary); }
      .text-muted { color: var(--text-muted); }
      .text-success { color: var(--success-color); }
      .text-danger { color: var(--danger-color); }
      .text-warning { color: var(--warning-color); }
      .text-info { color: var(--info-color); }

      .rounded-sm { border-radius: var(--radius-sm); }
      .rounded-md { border-radius: var(--radius-md); }
      .rounded-lg { border-radius: var(--radius-lg); }

      .shadow-sm { box-shadow: var(--shadow-sm); }
      .shadow-md { box-shadow: var(--shadow-md); }

      /* 简洁请求监控面板 */
      .request-monitor {
        background: var(--bg-primary);
        border: 1px solid var(--border-light);
        border-radius: var(--radius-lg);
        padding: var(--space-5);
      }

      .request-monitor .monitor-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: var(--space-5);
        padding: var(--space-3);
        background: var(--bg-secondary);
        border-radius: var(--radius-md);
      }

      .request-monitor .monitor-stats {
        display: flex;
        gap: var(--space-3);
        font-size: 0.75rem;
        color: var(--text-secondary);
      }

      .request-monitor .stat-item {
        display: flex;
        align-items: center;
        gap: var(--space-2);
        padding: var(--space-2) var(--space-3);
        background: var(--bg-primary);
        border: 1px solid var(--border-light);
        border-radius: var(--radius-sm);
      }

      .request-monitor .stat-dot {
        width: 8px;
        height: 8px;
        border-radius: var(--radius-full);
      }

      .request-monitor .stat-dot.success {
        background: var(--success-color);
      }
      .request-monitor .stat-dot.error {
        background: var(--danger-color);
      }
      .request-monitor .stat-dot.pending {
        background: var(--warning-color);
      }

      .request-monitor .monitor-controls {
        display: flex;
        gap: var(--space-2);
      }

      .request-monitor .request-filters {
        display: flex;
        gap: var(--space-3);
        margin-bottom: var(--space-5);
        padding: var(--space-3);
        background: var(--bg-secondary);
        border-radius: var(--radius-md);
      }

      .request-monitor .filter-input {
        flex: 1;
        padding: var(--space-2) var(--space-3);
        border: 1px solid var(--border-light);
        border-radius: var(--radius-sm);
        font-size: 0.75rem;
        background: var(--bg-primary);
        color: var(--text-primary);
        transition: all 0.2s var(--ease-out);
      }

      .request-monitor .filter-input:focus {
        outline: none;
        border-color: var(--primary-500);
        box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
      }

      .request-monitor .filter-select {
        padding: var(--space-2) var(--space-3);
        border: 1px solid var(--border-light);
        border-radius: var(--radius-sm);
        font-size: 0.75rem;
        background: var(--bg-primary);
        color: var(--text-primary);
        transition: all 0.2s var(--ease-out);
      }

      .request-monitor .request-list {
        max-height: 400px;
        overflow-y: auto;
        border: 1px solid var(--border-light);
        border-radius: var(--radius-md);
        background: var(--bg-primary);
      }

      .request-monitor .request-list::-webkit-scrollbar {
        width: 4px;
      }

      .request-monitor .request-list::-webkit-scrollbar-track {
        background: var(--bg-secondary);
        border-radius: var(--radius-sm);
      }

      .request-monitor .request-list::-webkit-scrollbar-thumb {
        background: var(--border-medium);
        border-radius: var(--radius-sm);
      }

      .request-monitor .request-item {
        padding: var(--space-3);
        border-bottom: 1px solid var(--border-light);
        cursor: pointer;
        transition: all 0.2s var(--ease-out);
      }

      .request-monitor .request-item:hover {
        background: var(--bg-secondary);
      }

      .request-monitor .request-item:last-child {
        border-bottom: none;
      }

      .request-monitor .request-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 0.25rem;
      }

      .request-monitor .request-method {
        padding: var(--space-1) var(--space-2);
        border-radius: var(--radius-sm);
        font-size: 0.625rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        border: 1px solid transparent;
        transition: all 0.2s var(--ease-out);
      }

      .request-monitor .request-method.GET {
        background: var(--info-color);
        color: white;
      }
      .request-monitor .request-method.POST {
        background: var(--success-color);
        color: white;
      }
      .request-monitor .request-method.PUT {
        background: var(--warning-color);
        color: white;
      }
      .request-monitor .request-method.DELETE {
        background: var(--danger-color);
        color: white;
      }
      .request-monitor .request-method.PATCH {
        background: var(--primary-600);
        color: white;
      }

      .request-monitor .request-status {
        display: flex;
        align-items: center;
        gap: var(--space-2);
        font-size: 0.75rem;
      }

      .request-monitor .status-code {
        padding: var(--space-1) var(--space-2);
        border-radius: var(--radius-sm);
        font-weight: 500;
        font-size: 0.625rem;
        border: 1px solid transparent;
      }

      .request-monitor .status-code.success {
        background: rgba(34, 197, 94, 0.1);
        color: var(--success-color);
        border-color: rgba(34, 197, 94, 0.2);
      }
      .request-monitor .status-code.error {
        background: rgba(239, 68, 68, 0.1);
        color: var(--danger-color);
        border-color: rgba(239, 68, 68, 0.2);
      }
      .request-monitor .status-code.pending {
        background: rgba(245, 158, 11, 0.1);
        color: var(--warning-color);
        border-color: rgba(245, 158, 11, 0.2);
      }

      .request-monitor .request-time {
        color: var(--text-muted);
        font-size: 0.625rem;
        background: var(--bg-secondary);
        padding: var(--space-1) var(--space-2);
        border-radius: var(--radius-sm);
      }

      .request-monitor .request-url {
        font-size: 0.75rem;
        color: var(--text-primary);
        word-break: break-all;
        word-wrap: break-word;
        overflow-wrap: break-word;
        margin-bottom: var(--space-2);
        overflow-x: hidden;
        max-width: 100%;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        background: var(--bg-secondary);
        padding: var(--space-2);
        border-radius: var(--radius-sm);
      }

      .request-monitor .request-meta {
        display: flex;
        justify-content: space-between;
        font-size: 0.625rem;
        color: var(--text-secondary);
        margin-top: var(--space-2);
      }

      .request-monitor .request-size,
      .request-monitor .request-duration {
        background: var(--bg-secondary);
        padding: var(--space-1) var(--space-2);
        border-radius: var(--radius-sm);
        display: flex;
        align-items: center;
        gap: var(--space-1);
      }

      .request-monitor .request-size::before {
        content: "📦";
        font-size: 0.75rem;
      }

      .request-monitor .request-duration::before {
        content: "⏱️";
        font-size: 0.75rem;
      }

      .request-monitor .empty-state {
        text-align: center;
        padding: var(--space-8);
        color: var(--text-secondary);
        background: var(--bg-secondary);
        border-radius: var(--radius-md);
      }

      .request-monitor .empty-icon {
        font-size: 3rem;
        margin-bottom: var(--space-3);
        opacity: 0.5;
      }

      .request-monitor .empty-text {
        font-size: 0.875rem;
        font-weight: 500;
      }
    `;
  }

  /**
   * 创建悬浮按钮
   */
  private createFloatingButton(): void {
    this.floatingButton = new FloatingButton(() => {
      this.showModal();
    });

    // 将悬浮按钮添加到Shadow DOM
    const buttonElement = this.floatingButton.getElement();
    this.shadow.appendChild(buttonElement);
  }

  /**
   * 显示主弹窗
   */
  private showModal(): void {
    if (this.modal) {
      this.modal.show();
      return;
    }

    this.modal = new Modal({
      width: 800,
      height: 650,
      showTitle: false,
      container: this.shadow, // 指定Shadow DOM作为容器
      onClose: () => {
        this.cleanup();
      }
    });

    // 创建标签页容器（标题栏模式）
    this.tabContainer = new TabContainer(true);

    // 获取标题栏的标签页容器
    const headerTabsContainer = this.modal.getModal().querySelector('.header-tabs') as HTMLElement;
    if (headerTabsContainer) {
      this.tabContainer.setTabsContainer(headerTabsContainer);
    }

    // 创建账号面板
    const panelState = {
      account: '',
      password: 'Authine@123456',
      token: '',
      loading: false
    };

    this.accountPanel = new AccountPanel(panelState, {
      onAccountLogin: async (account: string) => {
        // 处理账号登录逻辑
      },
      onTokenLogin: (token: string) => {
        // 处理Token登录逻辑
      },
      onClearData: () => {
        // 处理清除数据逻辑
      },
      onShowMessage: (message: string, type: string) => {
        // 处理消息显示逻辑
      }
    });

    // 创建请求监控
    this.requestMonitor = new RequestMonitor({
      onShowMessage: (message: string, type: string) => {
        // 处理消息显示逻辑
      }
    });

    // 添加标签页
    this.tabContainer.addTab('account', '账号切换', this.accountPanel.getContainer());
    this.tabContainer.addTab('monitor', '请求监控', this.requestMonitor.getContainer());

    // 设置模态框内容
    this.modal.setContent(this.tabContainer.getContainer());

    // 显示模态框
    this.modal.show();
  }

  /**
   * 清理资源
   */
  private cleanup(): void {
    this.modal = null;
    this.tabContainer = null;
    this.accountPanel = null;
    this.requestMonitor = null;
  }
}

// 注册自定义元素
customElements.define('account-change', AccountChangeElement);
