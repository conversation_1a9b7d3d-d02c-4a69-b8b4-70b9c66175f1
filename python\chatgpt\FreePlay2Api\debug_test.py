import requests
import json

def test_api():
    """测试API并查看调试信息"""
    print("开始测试API...")
    
    url = "http://localhost:8000/v1/chat/completions"
    data = {
        "model": "gpt-3.5-turbo",
        "messages": [{"role": "user", "content": "你好"}],
        "stream": False
    }
    
    try:
        response = requests.post(url, json=data)
        print(f"客户端收到状态码: {response.status_code}")
        result = response.json()
        print(f"客户端收到响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
    except Exception as e:
        print(f"客户端错误: {e}")

if __name__ == "__main__":
    test_api() 