import { req } from "./utils/Service"
import TaskQueue from "./utils/TaskQueue"
let href = location.href

if (String.prototype.splice === undefined) {
  /**
   * Splices text within a string.
   * @param {int} offset The position to insert the text at (before)
   * @param {string} text The text to insert
   * @param {int} [removeCount=0] An optional number of characters to overwrite
   * @returns {string} A modified string containing the spliced text.
   */
  String.prototype.splice = function (offset, text, removeCount = 0) {
    let calculatedOffset = offset < 0 ? this.length + offset : offset
    return (
      this.substring(0, calculatedOffset) +
      (text + "") +
      this.substring(calculatedOffset + removeCount)
    )
  }
}

// Your code here...
let global = unsafeWindow
let queue = new TaskQueue({ parallel: 2 })
let read_mores = [
  {
    url: /qq\.com/, //qq
    click: [".url-click"]
  },
  {
    url: /v2ex\.com/, //v2ex
    type: "interval",
    attrs: {
      ".cell .topic-link": {
        target: "_blank"
      }
    }
  },
  {
    url: /aliyundrive\.com/, //阿里云盘
    type: "interval",
    clicks: ["span[data-icon-type=PDSDrag]"],
    styles: {
      "ignore_cursor|#dplayer .dplayer-menu": "display=none"
    }
  },
  {
    url: /wuenci\.com|wpxz\.org|pan666|newxiaozhan\.com/, //阿里小站
    type: "interval",
    clicks: [".Alert-dismiss"],
    attrs: {
      "ignore_cursor|.DiscussionListItem .DiscussionListItem-main,a[rel~=nofollow]":
      {
        target: "_blank"
      },
      "tbody .thread_tit a.xst": {
        target: "_blank",
        onclick: ""
      }
    },
    styles: {
      '[class^="item-tag"]': {
        visibility: "visible"
      }
    },
    funcs: {
      ".DiscussionList-loadMore": ($e) => {
        if (isInViewPortOfTwo($e)) {
          let button = $e.querySelector(".Button")
          if (button) button.click()
        }
      }
    }
  },
  {
    url: /linux\.do/, //linux do
    type: "interval",
    attrs: {
      "ignore_cursor|tbody.topic-list-body .topic-list-item": {
        target: "_blank",
        onclick: "event.stopPropagation();console.log(event,'hello');return false;",
        onmousedown: "event.stopPropagation();return false;"
      },
      "ignore_cursor|tbody.topic-list-body .topic-list-item .link-top-line a.title": {
        target: "_blank",
        onclick: "event.stopPropagation();return false;",
        onmousedown: "event.stopPropagation();return false;"
      },
      "ignore_cursor|tbody.topic-list-body .topic-list-item td.main-link": {
        onclick: "event.stopPropagation();return false;",
        onmousedown: "event.stopPropagation();return false;"
      }
    },
  },
  {
    url: /magi\.com/,
    funcs: {
      ".pagination button span": ($e) => {
        if ($e.innerText === "加载更多") {
          if (isInViewPortOfTwo($e)) {
            $e.parentElement.click()
          }
        }
      }
    }
  },
  {
    url: /chongbuluo\.com/, //虫部落
    attrs: {
      "#threadlist .xst": {
        target: "_blank"
      }
    }
  },
  {
    url: /zhihu\.com/, //知乎
    type: "interval",
    modals: [
      ".LoadingBar",
      `.Topstory-mainColumn > a[class~='Card']`,
      `.TopstoryItem--advertCard`
    ],
    styles: {
      "#root .Topstory > div:not(.Topstory-container)": "display=none"
    },
    funcs: {
      ".actions .button": ($e) => {
        if ($e.innerText === "继续访问") {
          $e.click()
        }
      }
    },
    callback: (options) => {
      let cursor = options.cursor || 1
      let items = document.querySelectorAll(
        `.TopstoryItem:nth-child(n + ${cursor})`
      )
      if (items && items.length > 0) {
        cursor += items.length
        for (let item of items) {
          try {
            let vote = item.querySelector(".VoteButton")
            if (!vote) continue
            let label = vote.getAttribute("aria-label")
            label = label.replace("赞同 ", "")
            if (label) {
              if (label.indexOf("万") > 0) {
                continue
              }
              let num = parseInt(label)
              if (num > 30) {
                continue
              }
            }
            item.style.display = "none"
          } catch (e) {
            console.log(e)
          }
        }
      }
      options.cursor = cursor
    }
  },
  {
    url: /sina\.cn/, //新浪
    modals: [".look_more"],
    styles: {
      "body,.art_box": "overflow=auto"
    }
  },
  {
    url: /163\.com/, //163
    modals: [".show-more-wrap"],
    styles: {
      "body,.article-box": "overflow=auto"
    }
  },
  {
    url: /juejin\.cn/, //掘金
    funcs: {
      ".content .btn": ($e) => {
        if ($e.innerText === "继续访问") {
          $e.click()
        }
      }
    }
  },
  {
    url: /jianshu\.com/, //简书
    clicks: ["body > div > div:last-child > div"],
    modals: [".ant-btn"],
    styles: {
      "body,article": "height=100%;overflow=auto"
    }
  },
  {
    url: /baidu\.com/, //百度 "",
    modals: [
      ".read-whole-mask",
      ".chat-consult-box .circusee-bg",
      ".chat-consult-box .chat-consult-extandup",
      "#wgt-best .wgt-best-mask"
    ],
    styles: {
      "#exp-article .exp-content-container,#wgt-best .best-text":
        "height=100%;max-height=100%",
      ".chat-consult-box": "overflow=inherit;height=100%",
      ".chat-consult": "display=block"
    },
    clicks: [".show-answer-dispute span", "#show-answer-hide span"],
    funcs: {
      "#wgt-answers .wgt-answers-mask": ($e) => {
        $e.style.display = "none"
        $e.parentElement.style.height = "100%"
      }
    }
  },
  {
    url: /google\.com/, //google
    type: "interval",
    funcs: {
      "#search #rso .exp-outline": ($e) => $e.remove()
    },
    callback: (options) => {
      let results = document.querySelectorAll("#search [data-jsarwt]")
      results.forEach((result) => {
        let href = result.getAttribute("href")
        if (href.startsWith("http")) {
          result.setAttribute("data-jsarwt", 0)
        } else {
          let resp = /url=(.*)&/.exec(href)
          if (resp) {
            result.setAttribute("href", decodeURIComponent(resp[1]))
          }
        }
      })
      let searchs = document.querySelectorAll("#search h3")
      let regs = [/小.(百科|知识)网/, /.* - 小.(百科|知识)网/, /.* - 手机搜狐/]
      if (options.global_count != searchs.length) {
        options.global_count = searchs.length
      } else {
        return
      }

      for (let search of searchs) {
        let text = search.textContent
        let parent = search.closest(".g")
        for (let reg of regs) {
          if (reg.test(text)) {
            parent.remove()
            //parent.style.display = 'none'
          }
        }
      }
      let whites = document.querySelectorAll("#search .g ~ span")
      for (let white of whites) {
        white.remove()
      }

      let style = document.createElement("style")
      style.id = "mod"
      style.textContent = `
            .search-enhance-WDIEkP {width:100% !important}
            .chat-gpt-0rJcWY .chat-gpt-card-QCfkFJ .dialogue-7Y4kdh{height: 100%!important}
            `
      let enchance = document.querySelector("#c4g-search-enhance")
      if (enchance) {
        let shadowRoot = enchance.shadowRoot
        if (!shadowRoot.querySelector("style#mod")) {
          shadowRoot.appendChild(style)
        }
      }
    },
    injection: `
        #rcnt {max-width:98%;}
        #rcnt #chatgptbox-container {width: 800px;}
        `
  },
  {
    url: /didispace\.com/,
    modals: ["#read-more-wrap"],
    styles: {
      "body,.article,.article-inner": "overflow:auto"
    }
  },
  {
    url: /tencent\.com/, //腾讯
    modals: [".com-markdown-collpase-toggle"],
    styles: {
      "body,.com-markdown-collpase-main": "overflow=auto;maxHeight=100%"
    }
  },
  {
    url: /douyu\.com/, // 斗鱼
    callback: () => {
      var a = JSON.parse(localStorage.getItem("rateRecordTime_h5p_room"))
      a.v = "v"
      localStorage.setItem("rateRecordTime_h5p_room", JSON.stringify(a))
    }
  },
  {
    url: /huya\.com/, //虎牙
    modals: [".player-marquee-wrap"],
    callback: () => {
      localStorage.setItem("loginTipsCount", "v")
      document.querySelector(".player-videotype-list").children[0].click()
      setTimeout(() => {
        document.querySelector("#player-danmu-btn").click()
      }, 1000)
    }
  },
  {
    url: /jkmeng\.cn/,
    styles: {
      "#wrap,header": "filter=none"
    },
    callback: () => {
      let body = document.querySelector("body")
      body.removeChild(document.querySelector(".wp-czhmekaynkfxap-wrapper"))
      body.removeChild(document.querySelector(".wp-czhmekaynkfxap-blackout"))
    }
  },
  {
    url: /coolapk\.com/,
    styles: {
      "#nav-outer": "display=block"
    }
  },
  {
    url: /kuku\.lu/,
    styles: { ".adsbygoogle": "height=1px" }
  },
  {
    url: /bing\.com|microsoftonline\.com/,
    type: "interval",
    attrs: {
      //'ignore_cursor|.pageRecoContainer a,ignore_cursor|.b_algospacing_link a,ignore_cursor|.b_algo a,ignore_cursor|.ans_nws a': 'target=_blank',
      "ignore_cursor|#b_results h2 a": async ($e) => {
        $e.setAttribute("target", "_blank")
        let id = $e.getAttribute("id")
        if (id) return
        if ($e.getAttribute("hasAccess")) return
        queue.add(async () => {
          let href = $e.getAttribute("href")

          if (href && href.startsWith("https://www.bing.com/ck/a")) {
            try {
              let body = await req({ url: href }).catch((e) =>
                console.log(e, href, $e, "error")
              )
              if (body) {
                let match = /var\s*u\s*=\s*"(.*)"/.exec(body)
                if (match && match.length > 1) {
                  $e.setAttribute("href", match[1])
                }
              }
              href = $e.getAttribute("href")
              $e.setAttribute("hasAccess", 1)


              let html = $e.outerHTML
              html = html.splice(2, ` onclick="window.open('${href}')" `)
              html = html.splice(2, 1)

              html = html.splice(html.length - 1, 1)

              $e.outerHTML = html

            } catch (e) {
              console.log(e, "error")
            }
          }
          return true
        })
      }
    },
    styles: {
      ".msnpeek, #sw_as ~ div": "display=none",
      ".ans_nws .new_see_more_cjk": "justify-content=center;margin-left=0;color=white"
    },
    callback: (options) => {
      let cursor = options.cursor || 1
      if (cursor <= 1) {
        try {
          req({
            url: `https://www.bing.com/account/action?cc=hk`,
            method: "GET"
          })
        } catch (error) { }
      }

      options.cursor = 2

      let style = document.createElement("style")
      style.id = "mod"
      style.textContent = `
            .search-enhance-WDIEkP {width:100% !important}
            .chat-gpt-0rJcWY .chat-gpt-card-QCfkFJ .dialogue-7Y4kdh{height: 100%!important}
            `
      let enchance = document.querySelector("#c4g-search-enhance")
      if (enchance) {
        let shadowRoot = enchance.shadowRoot
        if (!shadowRoot.querySelector("style#mod")) {
          shadowRoot.appendChild(style)
        }
      }
    },
    injection: `
        body{overflow-x: hidden;}
        #b_content {display: flex; padding: 40px 40px 20px 160px;}
        #b_content aside:not([aria-label="更多结果"]){display:none;}
        #b_content main {display:flex; flex-direction: column;flex:1;max-width: 800px;}
        #b_content main #b_results{width: 100%!important;}
        #b_content aside {display:flex; flex-direction: column;margin-left:60px;flex:1;}
        #b_content #b_context {flex:1;width:100%;margin:0}
        #b_context .b_ans {margin:0;}
        a1:visited, #b_results>li a1:visited{color: #4007a2;}
        #b_results>li a1 {color: #4007a2; cursor: pointer;}

        :host {max-height: 100% !important;}
        :host([covered]) .fade {mask-image: none !important;}
        :host([covered]) .fade .container {max-height: 100% !important;}
        // #b_context { max-width: 900px !important; width: 100% !important;}
        // #b_context .b_ans {width:900px;padding:0!important;}
        // #b_context #chatgptbox-container {margin-left:-20px}
        // #b_context #chatgptbox-container *{ font-family: 'Roboto',Helvetica,Sans-Serif}
        // #b_context .b_ans .richrswrapper, #b_context .b_ans .utilAns, #b_context .b_ans .disambig-outline, #b_content .b_ans .wpvn_ala {display:none}
        // #b_context .b_ans .b_rrsr,#b_context .b_vidAns,#b_context .b_ans #tob_rail_container{display:none}
        // #b_context .optisearchbox {box-shadow: none;border: 1px solid #dadce0;max-width:900px}
        `
  },
  {
    url: /yuque\.com/,
    styles: { ".ant-modal-root": "display=none", body: "overflow=auto" }
  },
  {
    url: /mjjfaka\.net/,
    callback: (options) => {
      let cursor = options.cursor || 1
      if (cursor <= 1) {
        try {
          let items = document.querySelectorAll('a[data-url]')
          for (let item of items) {
            let url = item.getAttribute('data-url')
            if (!url.startsWith('http')) {
              url = `https://${url}`
            }

            item.setAttribute('href', url)
          }
        } catch (error) { }
      }

      options.cursor = 2
    }
  },
  {
    url: /magai\.co/,
    styles: {
      "body > .Page.main-page": "filter=none"
    }
  },
  {
    url: /panel\.local/,
    styles: { ".mx-\\[auto\\]": "display=none"}
  },
  {
    url: /.*/,
    type: "interval",
    callback: () => {
      let videos = document.querySelectorAll("video")
      if (videos && global.timer) {
        for (let video of videos) {
          let has_set_timer = video.getAttribute("has_set_timer")
          if (!has_set_timer) {
            video.setAttribute("has_set_timer", 1)
            let default_percentage = 1 / parseFloat(1.5)
            global.timer.change(default_percentage)
          }
        }
      }
    }
  },

]

document.addEventListener("readystatechange", () => {
  try {
    let running = localStorage.getItem("readmore_running")
    if (running === "1" || !document.title) {
      throw ""
    }
    localStorage.setItem("readmore_running", "1")

    for (let read_more of read_mores) {
      if (!read_more.url.test(href)) continue
      if (read_more.injection) {
        insertHeadStyle(
          read_more.injection,
          location.host.replaceAll(".", "_") + "_mod"
        )
      }
      let type = "timeout",
        timeout = 2000
      if (read_more.type) type = read_more.type
      if (read_more.timeout) timeout = read_more.timeout
      let exec = function (options) {
        let modals = read_more.modals
        setFromArr(modals, options, (e) => (e.style.display = "none"))

        let clicks = read_more.clicks
        setFromArr(clicks, options, (e) => e.click())

        let styles = read_more.styles
        setFromObj(
          styles,
          options,
          ($e, key, value) => ($e.style[key] = value)
        )

        let attrs = read_more.attrs
        setFromObj(attrs, options, ($e, key, value) => {
          $e.setAttribute(key, value)
        })

        let funcs = read_more.funcs
        setFromObj(funcs, options)

        let callback = read_more.callback
        if (callback && typeof callback === "function") {
          return callback(options)
        }
      }
      if (type === "timeout") {
        let _params = { type: "timeout" }
        setTimeout(() => {
          try {
            exec(_params)
          } catch (error) {
            console.log(error)
          }
        }, timeout)
      } else {
        let _params = { type: "interval" }
        exec(_params)
        setInterval(() => {
          try {
            exec(_params)
          } catch (error) {
            console.log(error)
          }
        }, timeout)
      }
    }
  } catch (e) {
  } finally {
    localStorage.removeItem("readmore_running")
  }

  setTimeout(() => localStorage.removeItem("readmore_running"), 0)
})

function parseConfig(str, config = {}) {
  let items = str.trim().split("|")
  if (items.length === 1) return str
  let len = items.length
  for (let i = 0; i < len - 1; i++) {
    let item = items[i]
    let _items = item.trim().split("=")
    let key = _items[0]
    if (_items.length === 1) {
      config[key] = 1
    } else {
      config[key] = _items[1]
    }
  }
  return items[len - 1]
}

function setFromArr(arr, options, func) {
  if (arr) {
    if (typeof arr === "string") arr = arr.split(",")
    arr.forEach((item) => {
      let config = {}
      item = parseConfig(item, config)
      let key = `${item}|cursor`
      let cursor = options[key] || 1
      if (options.type === "interval" && !config.ignore_cursor) {
        item = `${item}:nth-child(n+${cursor})`
      }
      let $item = document.querySelectorAll(item)
      if ($item) {
        cursor += $item.length
        $item.forEach((e) => {
          if (func && typeof func === "function") {
            func(e)
          }
        })
      }
      if (!config.ignore_cursor) options[key] = cursor
    })
  }
}

function setFromObj(obj, options, func) {
  if (obj) {
    for (let temp_key in obj) {
      let value = obj[temp_key]
      let multi_keys = temp_key.split(",")
      multi_keys.forEach((key, index) => {
        let config = {}
        key = parseConfig(key.trim(), config)

        let cursor_key = `${key}|cursor|${index}`
        let cursor = options[cursor_key] || 1
        if (options.type === "interval" && !config.ignore_cursor) {
          key = `${key.trim()}:nth-child(n+${cursor})`
        }
        let $eles = document.querySelectorAll(key)
        if ($eles && value) {
          cursor += $eles.length
          $eles.forEach(($e) => {
            if (typeof value === "string") {
              let items = value.split(";")
              for (let item of items) {
                let fields = item.split("=")
                if (func && typeof func === "function") {
                  func($e, fields[0], fields[1])
                }
              }
            } else if (typeof value === "function") {
              value($e)
            } else {
              for (let field in value) {
                if (func && typeof func === "function") {
                  func($e, field, value[field])
                }
              }
            }
          })
        }
        if (!config.ignore_cursor) options[cursor_key] = cursor
      })
    }
  }
}

function isInViewPortOfTwo(el) {
  const viewPortHeight =
    window.innerHeight ||
    document.documentElement.clientHeight ||
    document.body.clientHeight
  const top = el.getBoundingClientRect() && el.getBoundingClientRect().top
  return top <= viewPortHeight + 100
}

function insertHeadStyle(css, className = "") {
  let headFrag = document.createDocumentFragment()
  headFrag.appendChild(create_CSS_Node(css, className))
  if (document.head) document.head.appendChild(headFrag)
}

function create_CSS_Node(css, className = "", initType = "text/css") {
  let cssNode = document.createElement("style")
  if (className) {
    cssNode.className = className
    const xclass = "." + className.split(" ").join(".")
    cssNode.dataset.xclass = xclass
  }

  cssNode.setAttribute("type", initType)
  cssNode.appendChild(document.createTextNode(css))

  return cssNode
}
