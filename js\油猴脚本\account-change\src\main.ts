// 主入口文件 - Shadow DOM实现
import './components/AccountChangeElement';

/**
 * 初始化账号切换插件
 * 使用Shadow DOM实现完全的样式隔离
 */
function initAccountChangePlugin(): void {
  // 检查是否已经初始化
  if (document.querySelector('account-change')) {
    return;
  }

  // 创建自定义元素实例
  const accountChangeElement = document.createElement('account-change');

  // 添加到页面
  document.body.appendChild(accountChangeElement);
}

// 等待DOM加载完成后启动应用
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initAccountChangePlugin);
} else {
  initAccountChangePlugin();
}
