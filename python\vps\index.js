const http = require('http')
const fs = require('fs')
const { Buffer } = require('buffer')
const net = require('net')
const { WebSocket, createWebSocketStream } = require('ws')

// 配置参数
const UUID = process.env.UUID || 'fd6ea364-06c7-4ed9-93e2-9653824e638d'    // UUID用于验证连接
const DOMAIN = process.env.DOMAIN || 'proxy.came.dpdns.org'    // 域名
const CF_DOMAIN = process.env.CF_DOMAIN || 'www.visa.com.tw'    // 反代域名
const SUB_PATH = process.env.SUB_PATH || 'sub'     // 获取节点的订阅路径
const NAME = process.env.NAME || 'us-01'  // 节点名称
const PORT = process.env.PORT || 3000     // http和ws服务端口

const httpServer = http.createServer((req, res) => {
    // 记录请求信息，帮助调试
    console.log(`收到请求: ${req.method} ${req.url}`)

    // 处理/sub路径，不区分大小写
    if (req.url === '/') {
        res.writeHead(200, { 'Content-Type': 'text/plain' })
        res.end('It works!\nNodeJS ' + process.versions.node + '\n')
    } else if (req.url.toLowerCase() === `/${SUB_PATH.toLowerCase()}`) {
        console.log('处理订阅请求...')
        console.log(`UUID: ${UUID}, DOMAIN: ${DOMAIN}, NAME: ${NAME}`)

        const nodeName = NAME || 'NodeWS'
        const vlessURL = `vless://${UUID}@${CF_DOMAIN}:443?encryption=none&security=tls&sni=${DOMAIN}&type=ws&host=${DOMAIN}&path=%2F#${nodeName}`

        console.log(`生成的VLESS URL: ${vlessURL}`)
        const base64Content = Buffer.from(vlessURL).toString('base64')
        console.log(`Base64编码后: ${base64Content}`)

        res.writeHead(200, {
            'Content-Type': 'text/plain',
            'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
        })
        res.end(base64Content + '\n')
    } else if (req.url === '/status') {
        // 添加状态检查端点
        const { exec } = require('child_process')
        exec('ps aux | grep -v grep | grep "node\\|nezha-agent"', (error, stdout) => {
            res.writeHead(200, { 'Content-Type': 'text/plain' })
            res.end(`服务状态:\n${stdout}\n`)
        })
    } else if (req.url === '/debug') {
        // 添加调试端点
        res.writeHead(200, { 'Content-Type': 'text/plain' })
        res.end(`调试信息:
UUID: ${UUID}
DOMAIN: ${DOMAIN}
SUB_PATH: ${SUB_PATH}
NAME: ${NAME}
PORT: ${PORT}
`)
    } else {
        res.writeHead(404, { 'Content-Type': 'text/plain' })
        res.end('Not Found\n')
    }
})

const wss = new WebSocket.Server({ server: httpServer })
const uuid = UUID.replace(/-/g, "")
wss.on('connection', ws => {
    ws.once('message', msg => {
        const [VERSION] = msg
        const id = msg.slice(1, 17)
        if (!id.every((v, i) => v == parseInt(uuid.substr(i * 2, 2), 16))) return
        let i = msg.slice(17, 18).readUInt8() + 19
        const port = msg.slice(i, i += 2).readUInt16BE(0)
        const ATYP = msg.slice(i, i += 1).readUInt8()
        const host = ATYP == 1 ? msg.slice(i, i += 4).join('.') :
            (ATYP == 2 ? new TextDecoder().decode(msg.slice(i + 1, i += 1 + msg.slice(i, i + 1).readUInt8())) :
                (ATYP == 3 ? msg.slice(i, i += 16).reduce((s, b, i, a) => (i % 2 ? s.concat(a.slice(i - 1, i + 1)) : s), []).map(b => b.readUInt16BE(0).toString(16)).join(':') : ''))
        ws.send(new Uint8Array([VERSION, 0]))
        const duplex = createWebSocketStream(ws)
        net.connect({ host, port }, function () {
            this.write(msg.slice(i))
            duplex.on('error', () => { }).pipe(this).on('error', () => { }).pipe(duplex)
        }).on('error', () => { })
    }).on('error', () => { })
})

httpServer.listen(PORT, () => {
    console.log(`Server is running on port ${PORT}`)
})
