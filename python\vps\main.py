import os
import base64
import json
import asyncio
import subprocess
import threading
import socket
import struct
import http.server
import socketserver
import urllib.parse
import time
import hashlib
import re
import ssl
from http import HTTPStatus
from io import BytesIO

# 配置参数
UUID = os.environ.get('UUID', 'fd6ea364-06c7-4ed9-93e2-9653824e638d')  # UUID用于验证连接
DOMAIN = os.environ.get('DOMAIN', 'proxy.came.dpdns.org')              # 域名
CF_DOMAIN = os.environ.get('CF_DOMAIN', 'www.visa.com.tw')             # 反代域名
SUB_PATH = os.environ.get('SUB_PATH', 'sub')                           # 获取节点的订阅路径
NAME = os.environ.get('NAME', 'us-01')                                 # 节点名称
PORT = int(os.environ.get('PORT', 3000))                               # 服务端口

class HTTPRequestHandler(http.server.BaseHTTPRequestHandler):
    """HTTP请求处理器，替代Flask应用"""

    def _set_headers(self, status_code=HTTPStatus.OK, content_type='text/plain'):
        """设置HTTP响应头"""
        self.send_response(status_code)
        self.send_header('Content-Type', content_type)
        self.end_headers()

    def _set_no_cache_headers(self):
        """设置禁止缓存的HTTP响应头"""
        self.send_header('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate')
        self.send_header('Pragma', 'no-cache')
        self.send_header('Expires', '0')

    def do_GET(self):
        """处理GET请求"""
        # 解析URL路径
        parsed_path = urllib.parse.urlparse(self.path)
        path = parsed_path.path.lower()

        print(f"收到请求: GET {self.path}")

        # 处理根路径
        if path == '/':
            self._set_headers()
            self.wfile.write(f'It works!\nPython {os.environ.get("PYTHON_VERSION", "3.x")}\n'.encode())

        # 处理订阅路径
        elif path == f'/{SUB_PATH.lower()}':
            print('处理订阅请求...')
            print(f'UUID: {UUID}, DOMAIN: {DOMAIN}, NAME: {NAME}')

            node_name = NAME or 'NodeWS'
            vless_url = f'vless://{UUID}@{CF_DOMAIN}:443?encryption=none&security=tls&sni={DOMAIN}&type=ws&host={DOMAIN}&path=%2F#{node_name}'

            print(f'生成的VLESS URL: {vless_url}')
            base64_content = base64.b64encode(vless_url.encode()).decode()
            print(f'Base64编码后: {base64_content}')

            self._set_headers()
            self._set_no_cache_headers()
            self.wfile.write(f'{base64_content}\n'.encode())

        # 处理状态检查
        elif path == '/status':
            try:
                result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
                output = result.stdout
                filtered_output = '\n'.join([line for line in output.split('\n')
                                            if 'python' in line.lower() and 'grep' not in line.lower()])

                self._set_headers()
                self.wfile.write(f'服务状态:\n{filtered_output}\n'.encode())
            except Exception as e:
                self._set_headers()
                self.wfile.write(f'获取状态失败: {str(e)}\n'.encode())

        # 处理调试请求
        elif path == '/debug':
            debug_info = f'''调试信息:
UUID: {UUID}
DOMAIN: {DOMAIN}
SUB_PATH: {SUB_PATH}
NAME: {NAME}
PORT: {PORT}
'''
            self._set_headers()
            self.wfile.write(debug_info.encode())

        # 处理404
        else:
            self._set_headers(HTTPStatus.NOT_FOUND)
            self.wfile.write('Not Found\n'.encode())

    def log_message(self, format, *args):
        """自定义日志输出"""
        print(f"{self.client_address[0]} - [{time.strftime('%Y-%m-%d %H:%M:%S')}] {format%args}")

class WebSocketProtocol:
    """WebSocket协议处理工具类"""

    @staticmethod
    def create_accept_key(key):
        """生成WebSocket握手的Accept Key"""
        GUID = '258EAFA5-E914-47DA-95CA-C5AB0DC85B11'
        hash_key = key + GUID
        sha1 = hashlib.sha1(hash_key.encode()).digest()
        return base64.b64encode(sha1).decode()

    @staticmethod
    def parse_frame(data):
        """解析WebSocket数据帧"""
        fin = (data[0] & 0x80) >> 7
        opcode = data[0] & 0x0F
        mask = (data[1] & 0x80) >> 7
        payload_len = data[1] & 0x7F

        # 计算payload起始索引
        header_len = 2
        if payload_len == 126:
            payload_len = struct.unpack("!H", data[2:4])[0]
            header_len = 4
        elif payload_len == 127:
            payload_len = struct.unpack("!Q", data[2:10])[0]
            header_len = 10

        # 处理掩码
        if mask:
            mask_key = data[header_len:header_len+4]
            header_len += 4
            payload = bytearray(data[header_len:header_len+payload_len])
            for i in range(len(payload)):
                payload[i] = payload[i] ^ mask_key[i % 4]
            return fin, opcode, payload
        else:
            payload = data[header_len:header_len+payload_len]
            return fin, opcode, payload

    @staticmethod
    def create_frame(opcode, payload, fin=True):
        """创建WebSocket数据帧"""
        frame = bytearray()

        # 添加FIN和opcode
        frame.append((0x80 if fin else 0x00) | opcode)

        # 添加payload长度
        payload_len = len(payload)
        if payload_len <= 125:
            frame.append(payload_len)
        elif payload_len <= 65535:
            frame.append(126)
            frame.extend(struct.pack("!H", payload_len))
        else:
            frame.append(127)
            frame.extend(struct.pack("!Q", payload_len))

        # 添加payload
        if isinstance(payload, str):
            frame.extend(payload.encode())
        else:
            frame.extend(payload)

        return bytes(frame)

class WebSocketServer:
    """原生实现的WebSocket服务器"""

    def __init__(self):
        self.server_socket = None

    async def handle_client(self, client, addr):
        """处理客户端连接"""
        print(f"客户端连接: {addr}")

        try:
            # 先接收并处理HTTP握手请求
            request_data = await self.receive_data(client)
            if not request_data:
                return

            # 检查是否为WebSocket握手请求
            if not self.is_websocket_handshake(request_data):
                print(f"非WebSocket握手请求")
                client.close()
                return

            # 发送WebSocket握手响应
            key = self.extract_websocket_key(request_data)
            if not key:
                print(f"无效的WebSocket握手请求")
                client.close()
                return

            accept_key = WebSocketProtocol.create_accept_key(key)
            handshake_response = (
                "HTTP/1.1 101 Switching Protocols\r\n"
                "Upgrade: websocket\r\n"
                "Connection: Upgrade\r\n"
                f"Sec-WebSocket-Accept: {accept_key}\r\n\r\n"
            )
            client.send(handshake_response.encode())

            # 接收第一条消息，处理VLESS协议
            msg = await self.receive_websocket_frame(client)
            if not msg:
                return

            # 解析VLESS协议
            VERSION = msg[0]
            uuid_bytes = msg[1:17]

            # 验证UUID
            uuid_str = UUID.replace('-', '')
            uuid_check = bytes.fromhex(uuid_str)

            if uuid_bytes != uuid_check:
                print("UUID验证失败")
                client.close()
                return

            # 解析地址和端口
            try:
                i = msg[17] + 19
                port = struct.unpack('>H', msg[i:i+2])[0]
                i += 2

                ATYP = msg[i]
                i += 1

                if ATYP == 1:  # IPv4
                    host = '.'.join([str(b) for b in msg[i:i+4]])
                    i += 4
                elif ATYP == 2:  # 域名
                    length = msg[i]
                    i += 1
                    host = msg[i:i+length].decode('utf-8')
                    i += length
                elif ATYP == 3:  # IPv6
                    ipv6_bytes = msg[i:i+16]
                    i += 16
                    host = ':'.join([f'{ipv6_bytes[j]<<8|ipv6_bytes[j+1]:04x}' for j in range(0, 16, 2)])
                else:
                    print(f"不支持的地址类型: {ATYP}")
                    client.close()
                    return

                print(f"连接到 {host}:{port}")
            except Exception as e:
                print(f"解析地址错误: {e}")
                client.close()
                return

            # 发送响应
            client.send(WebSocketProtocol.create_frame(0x2, bytes([VERSION, 0])))

            # 创建到目标的连接
            try:
                target_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                await self.connect_target(target_socket, host, port)
            except Exception as e:
                print(f"连接目标失败: {e}")
                client.close()
                return

            # 发送剩余数据
            if i < len(msg):
                target_socket.send(msg[i:])

            # 设置双向数据转发
            await asyncio.gather(
                self.forward(client, target_socket, is_ws=True),
                self.forward(target_socket, client, is_ws=False)
            )

        except Exception as e:
            print(f"处理客户端连接错误: {e}")
        finally:
            try:
                client.close()
            except:
                pass

    async def connect_target(self, sock, host, port):
        """异步连接到目标服务器"""
        sock.setblocking(False)
        try:
            sock.connect((host, port))
        except BlockingIOError:
            pass

        # 等待连接完成
        loop = asyncio.get_event_loop()
        await loop.sock_connect(sock, (host, port))

    async def receive_data(self, sock, buffer_size=8192):
        """异步接收数据"""
        loop = asyncio.get_event_loop()
        try:
            data = await loop.sock_recv(sock, buffer_size)
            return data
        except Exception as e:
            print(f"接收数据错误: {e}")
            return None

    async def receive_websocket_frame(self, sock):
        """接收并解析WebSocket数据帧"""
        # 先接收头部
        header = await self.receive_data(sock, 2)
        if not header or len(header) < 2:
            return None

        # 解析payload长度
        payload_len = header[1] & 0x7F
        mask_present = bool(header[1] & 0x80)

        extended_len = 0
        if payload_len == 126:
            extended_data = await self.receive_data(sock, 2)
            if not extended_data:
                return None
            extended_len = struct.unpack('!H', extended_data)[0]
        elif payload_len == 127:
            extended_data = await self.receive_data(sock, 8)
            if not extended_data:
                return None
            extended_len = struct.unpack('!Q', extended_data)[0]
        else:
            extended_len = payload_len

        # 读取掩码
        mask = None
        if mask_present:
            mask = await self.receive_data(sock, 4)
            if not mask:
                return None

        # 读取payload
        payload = b''
        remaining = extended_len
        while remaining > 0:
            chunk = await self.receive_data(sock, min(remaining, 8192))
            if not chunk:
                return None
            payload += chunk
            remaining -= len(chunk)

        # 应用掩码
        if mask:
            payload = bytearray(payload)
            for i in range(len(payload)):
                payload[i] ^= mask[i % 4]

        return bytes(payload)

    async def forward(self, source, target, is_ws=False):
        """转发数据"""
        try:
            while True:
                if is_ws:
                    # WebSocket客户端到目标服务器
                    data = await self.receive_websocket_frame(source)
                    if not data:
                        break
                    await self.send_data(target, data)
                else:
                    # 目标服务器到WebSocket客户端
                    data = await self.receive_data(source)
                    if not data:
                        break
                    frame = WebSocketProtocol.create_frame(0x2, data)
                    await self.send_data(target, frame)
        except Exception as e:
            print(f"转发错误: {e}")
        finally:
            try:
                source.close()
            except:
                pass
            try:
                target.close()
            except:
                pass

    async def send_data(self, sock, data):
        """异步发送数据"""
        loop = asyncio.get_event_loop()
        await loop.sock_sendall(sock, data)

    def is_websocket_handshake(self, data):
        """检查是否是WebSocket握手请求"""
        return (b'GET' in data and
                b'Upgrade: websocket' in data and
                b'Connection: Upgrade' in data and
                b'Sec-WebSocket-Key:' in data)

    def extract_websocket_key(self, data):
        """从握手请求中提取WebSocket Key"""
        lines = data.split(b'\r\n')
        for line in lines:
            if b'Sec-WebSocket-Key:' in line:
                key = line.split(b'Sec-WebSocket-Key:')[1].strip().decode()
                return key
        return None

    async def listen(self, host='0.0.0.0', port=None):
        """启动WebSocket服务器监听"""
        if port is None:
            port = PORT

        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind((host, port))
            self.server_socket.listen(100)
            self.server_socket.setblocking(False)

            print(f"WebSocket服务启动在端口 {port}")

            loop = asyncio.get_event_loop()
            while True:
                client, addr = await loop.sock_accept(self.server_socket)
                loop.create_task(self.handle_client(client, addr))
        except Exception as e:
            print(f"WebSocket服务器错误: {e}")
        finally:
            if self.server_socket:
                self.server_socket.close()

class Server:
    """集成HTTP和WebSocket服务的主服务器类"""

    def __init__(self):
        # 使用简单的HTTP服务器
        handler = HTTPRequestHandler
        self.http_server = socketserver.ThreadingTCPServer(('0.0.0.0', PORT+1), handler)

        # WebSocket服务器
        self.ws_server = WebSocketServer()

    def run_http_server(self):
        """运行HTTP服务器"""
        print(f"HTTP服务启动在端口 {PORT+1}")
        self.http_server.serve_forever()

    def run(self):
        """启动服务器"""
        # 确保issues目录存在
        if not os.path.exists('./issues'):
            os.makedirs('./issues')

        # 启动HTTP服务器在单独的线程中
        http_thread = threading.Thread(target=self.run_http_server)
        http_thread.daemon = True
        http_thread.start()

        # 启动WebSocket服务器在主线程
        loop = asyncio.get_event_loop()
        try:
            loop.run_until_complete(self.ws_server.listen(port=PORT))
        except KeyboardInterrupt:
            print("服务器正在关闭...")
        finally:
            self.http_server.shutdown()
            loop.close()

# 主程序入口
if __name__ == '__main__':
    server = Server()
    server.run()