import aiohttp
import asyncio
import json


headers = {
    "accept": "application/json",
    "accept-language": "zh-CN,zh;q=0.9",
    "content-type": "application/json",
    "referer": "https://app.freeplay.ai/projects",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36"
}

async def get_projects(session_value):
    """获取项目信息"""
    url = "https://app.freeplay.ai/app_data/globals"
    cookies = {"session": session_value}
    
    async with aiohttp.ClientSession() as session:
        async with session.get(url, headers=headers, cookies=cookies) as response:
            response_text = await response.text()
            print(f"获取项目响应: {response_text}")
            
            if response.status == 200:
                response_json = await response.json()
                all_projects = response_json.get("all_projects", [])
                if all_projects:
                    project_id = all_projects[0]["id"]
                    print(f"✅ 获取到项目ID: {project_id}")
                    return project_id
                else:
                    print("❌ 没有找到项目")
                    return None
            else:
                print(f"❌ 获取项目失败，状态码: {response.status}")
                return None

if __name__ == "__main__":
    # 测试用的session值
    test_session = "jUUixpzdGIi1Z9hHF3JG2c7f41vu547XQPYa3_3K17s"
    asyncio.run(get_projects(test_session))